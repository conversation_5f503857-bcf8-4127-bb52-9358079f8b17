import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

/**
 * 导出包含多列图片的JSON数据到Excel
 * @param {Array} data - 数据数组
 * @param {string} filename - 文件名
 * @param {Object} headers - 表头映射
 * @param {Array} imageConfigs - 图片配置数组
 */
export const exportJsonToExcelWithImages = async (data, filename = 'export', headers = {}, imageConfigs = []) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');

    // 设置表头
    const headerKeys = Object.keys(headers);
    const headerValues = Object.values(headers);

    worksheet.addRow(headerValues);

    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF4F81BD' }
      };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });

    // 添加数据行
    for (let i = 0; i < data.length; i++) {
      const rowData = data[i];
      const rowValues = headerKeys.map(key => {
        // 如果是图片列，留空
        if (imageConfigs.some(config => config.imageColumn === key) && rowData[key]) {
          return '';
        }
        return rowData[key] || '';
      });

      const row = worksheet.addRow(rowValues);

      // 设置行高（取最大的行高配置）
      const maxRowHeight = Math.max(...imageConfigs.map(config => config.rowHeight || 80), 80);
      row.height = maxRowHeight;

      // 为每个图片列添加图片
      for (const imageConfig of imageConfigs) {
        if (imageConfig.imageColumn && rowData[imageConfig.imageColumn]) {
          await addImageToCell(
            worksheet,
            rowData[imageConfig.imageColumn],
            i + 2,
            headerKeys.indexOf(imageConfig.imageColumn) + 1,
            imageConfig,
            workbook
          );
        }
      }
    }

    // 调整列宽
    worksheet.columns = headerKeys.map((key) => {
      const imageConfig = imageConfigs.find(config => config.imageColumn === key);
      const width = imageConfig ? (imageConfig.columnWidth || 15) : 20;
      return { width };
    });

    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    saveAs(blob, `${filename}_${new Date().getTime()}.xlsx`);
    return true;
  } catch (error) {
    console.error('导出Excel失败:', error);
    return false;
  }
};

/**
 * 添加图片到单元格
 */
const addImageToCell = async (worksheet, imageUrl, rowNumber, columnNumber, config, workbook) => {
  try {
    // 获取图片
    const response = await fetch(imageUrl);
    const imageBuffer = await response.arrayBuffer();

    // 获取图片扩展名
    const extension = getImageExtension(imageUrl);

    // 添加图片到工作簿
    const imageId = workbook.addImage({
      buffer: imageBuffer,
      extension: extension
    });

    // 计算图片位置和大小
    const cellWidth = config.cellWidth || 100;
    const cellHeight = config.cellHeight || 60;

    worksheet.addImage(imageId, {
      tl: { col: columnNumber - 1, row: rowNumber - 1 },
      br: { col: columnNumber, row: rowNumber },
      editAs: 'oneCell'
    });

  } catch (error) {
    console.warn('添加图片失败:', imageUrl, error);
  }
};

/**
 * 获取图片扩展名
 */
const getImageExtension = (url) => {
  if (url.startsWith('data:image')) {
    return url.split(';')[0].split('/')[1];
  }

  const ext = url.split('.').pop().toLowerCase();
  return ext === 'jpg' ? 'jpeg' : ext;
};
