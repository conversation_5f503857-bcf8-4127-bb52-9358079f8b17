/**
 * MineAdmin is committed to providing solutions for quickly building web applications
 * Please view the LICENSE file that was distributed with this source code,
 * For the full copyright and license information.
 * Thank you very much for using MineAdmin.
 *
 * <AUTHOR>
 * @Link   https://github.com/mineadmin
 */
import App from './App.vue'
import MineBootstrap from './bootstrap'
import '@/utils/dateUtil.js'
import CopyText from "~/base/views/components/copyText.vue";
import SvgIcon from "~/base/views/components/svgIcon.vue";

import MyPlugin from '@/utils/vuePlugsPrint.js'


const app = createApp(App)
app.component('CopyText', CopyText);
app.component('SvgIcon', SvgIcon);
app.use(MyPlugin)

MineBootstrap(app).then(() => {
  app.mount('#app')
}).catch((err) => {
  console.error('MineAdmin-UI start fail', err)
})
