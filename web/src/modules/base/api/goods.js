export function getGoodsList(params) {
  return useHttp().get('/admin/goods/goods/list',{params})
}
export function getPushGoodsList(params) {
  return useHttp().get('/admin/goods/goods/merchant-push-list',{params})
}

export function addProduct(data) {
  return useHttp().post('/admin/product/product',data)
}

export function getProducts(params) {
  return useHttp().get('/admin/product/product/list',{params})
}
export function getFactoryProducts(params) {
  return useHttp().get('/admin/product/product/supplier-list',{params})
}

export function deleteProduct(id) {
  return useHttp().delete('/admin/product/product',{data: {id}})
}

export function deleteProductSku(id) {
  return useHttp().delete('/admin/product/product_sku',{data: {id}})
}

export function bindGoodProduct(data) {
  return useHttp().post('/admin/goods/goods_product_relation',data)
}

export function bindGoodProducts(data) {
  return useHttp().post('/admin/goods/goods_product_relation/batch',data)
}

export function deleteRelation(data) {
  return useHttp().delete('/admin/goods/goods_product_relation',{data})
}

export function checkSkuBindFactory(data) {
  return useHttp().post('/admin/goods/goods_product_relation/check', data)
}

//备货单
export function stockPrepareGoods(data){
  return useHttp().post('/admin/order/statistics/stockPrepare', data)
}
export function stockPrepareProduct(data){
  return useHttp().post('/admin/order/statistics/stockPrepareProduct', data)
}
