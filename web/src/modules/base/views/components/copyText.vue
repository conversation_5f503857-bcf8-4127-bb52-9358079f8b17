<script setup>
import {ElMessage} from "element-plus";

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
});

function copyValue(){
  const input = document.createElement("input");
  document.body.appendChild(input);

  input.setAttribute("readonly", "readonly");
  input.setAttribute("value", props.value);
  input.select();

  if (document.execCommand("copy")) {
    document.execCommand("copy");
    ElMessage.success(`已复制：${props.value}`);
  } else {
    ElMessage.warning("您的浏览器暂不支持一键复制哦");
  }
  document.body.removeChild(input);
}
</script>


<template>
 <span class="copy-wrapper">
   <span>{{value}}</span>
   <ma-svg-icon name="material-symbols:content-copy-outline" :size="14" @click="copyValue" class="pointer ml-1 blue"/>
 </span>
</template>

<style lang="scss">
.copy-wrapper{
  display: inline-flex;
  align-items: center;
}
</style>
