<script setup>
import {TYPE_LABEL} from '@/utils/commonData.js'

const props = defineProps({
  modelValue: {
    type: [Number, String, Array],
    default: null,
  },
  placeholder: {
    type: String,
    default: '请选择来源',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  includeFactory: {
    type: Boolean,
    default: false,
  },
  includeMerchant: {
    type: Boolean,
    default: false,
  },
  includeSelf: {
    type: Boolean,
    default: true,
  },
  includePlatformShop: {
    type: Boolean,
    default: true,
  }
});

const value1 = computed({
  get() {
    return props.modelValue;
  },
  set(v) {
    emit('update:modelValue', v);
  },
});
const emit = defineEmits(['update:modelValue']);
const shopList = useUserStore().getShops()
const dataList = computed(() => {
  let list = [...shopList]
  if (!props.includePlatformShop) {
    list = list.filter(it=>it.type === 99)
  }
  if (!props.includeSelf) {
    list = list.filter(it=>it.type !== 99)
  }

  list = list?.map(it => {
    const obj = {value: 'shop_' + it.id, label: '【' + TYPE_LABEL[it.type] + '】' + it.shop_name}
    if (it.type === 99) {
      obj.label = '自定义店铺'
    }
    return obj
  })
  if (props.includeMerchant) {
    merchantList.forEach(it => {
      list.push({value: 'merchant_' + it.related_user.id, label: it.name})
    })
  }
  if (props.includeFactory) {
    factoryList.forEach(it => {
      list.push({value: 'factory_' + it.related_user.id, label: it.name})
    })
  }

  return list
})

const merchantList = useUserStore().getMerchants()
const factoryList = useUserStore().getFactorys()
</script>

<template>
  <el-select v-model="value1" :placeholder="props.placeholder" :multiple="multiple" :max-tag-count='1'>
    <el-option v-if="dataList?.length" v-for="it in dataList" :key="it.value" :value="it.value" :label="it.label"/>
  </el-select>
</template>

<style lang="scss" scoped>
.shop-select-wrapper_logo_img {
  display: inline-block;
  margin-right: 8px;
  width: 24px;
  height: 24px;
}
</style>
