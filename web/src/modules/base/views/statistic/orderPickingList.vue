<script setup>
import {getUserSetting} from "~/base/api/user.js";
import GoodsPickingList from "~/base/views/statistic/goodsPickingList.vue";
import ProductPickingList from "~/base/views/statistic/productPickingList.vue";
import {exportJsonToExcelWithImages} from '@/utils/exportExcel';

const beihuoTemplate = ref({})
const dbExportConfig = ref({})

onMounted(async () => {
  let exportConfig = localStorage.getItem('beihuo_export_set')
  if (exportConfig) {
    dbExportConfig.value = JSON.parse(exportConfig)
  }
  await getConfig()
  changeType()
})



async function getConfig() {
  const res = await getUserSetting()
  let beihuoContents = {}
  if (res.data?.stock_config) {
    beihuoContents = JSON.parse(res.data.stock_config)
  }
  beihuoTemplate.value = beihuoContents
}

const groupType = ref(1)
const typeList = [
  {label: '平台商品维度', value: 1},
  {label: '系统货品维度', value: 2}
]

const goodsPickingRef =ref()
const productPickingRef =ref()

function changeType(){
  switch (groupType.value) {
    case 1:
      exportColumns.value = dbExportConfig.value.goods?.length ? dbExportConfig.value.goods : [...defaultGoodsExportColumns]
      goodsPickingRef.value.init(beihuoTemplate.value)
      break;
    case 2:
      exportColumns.value = dbExportConfig.value.product?.length ? dbExportConfig.value.product : [...defaultProductExportColumns]
      productPickingRef.value.init(beihuoTemplate.value)
      break;
   default:
      break;
  }
}
const {
  getMenuCollapseState
} = useSettingStore()

function printPickingList(){
  switch (groupType.value) {
    case 1:
      goodsPickingRef.value.print()
      break;
    case 2:
      productPickingRef.value.print()
      break;
    default:
      break;
  }
}

const exportModal = ref(false)
const originGoodsExportColumns = [
  {
    title: "商品ID",
    key: "num_iid",
    type: "text",
  },
  {
    title: "商品名称",
    key: "goods_title",
    type: "text",
  }, {
    title: "商品简称",
    key: "customTitle",
    type: "text",
  }, {
    title: "商品图片",
    key: "goods_pic",
    type: "image",
    width: 80,
    height: 80,
  }, {
    title: "商品编码",
    key: "outer_goods_id",
    type: "text",
  }, {
    title: "规格ID",
    key: "sku_id",
    type: "text",
  }, {
    title: "规格名称",
    key: "sku_value",
    type: "text",
  },
  {
    title: "规格简称",
    key: "customSkuValue",
    type: "text",
  }, {
    title: "规格图片",
    key: "sku_pic",
    type: "image",
    width: 80,
    height: 80,
  }, {
    title: "规格编码",
    key: "outer_id",
    type: "text",
  }, {
    title: "数量",
    key: "skuNum",
    type: "text",
  }, {
    title: "金额",
    key: "skuPayment",
    type: "text",
  }, {
    title: "总数",
    key: "total_num",
    type: "text",
  }, {
    title: "总金额",
    key: "total_payment",
    type: "text",
  }, {
    title: "备注",
    key: "remark",
    type: "text",
  }
]
const originProductExportColumns = [
  {
    title: "货品名称",
    key: "product_name",
    type: "text",
  }, {
    title: "货品编码",
    key: "product_no",
    type: "text",
  }, {
    title: "规格名称",
    key: "sku_name",
    type: "text",
  }, {
    title: "规格图片",
    key: "image_url",
    type: "image",
    width: 80,
    height: 80,
  }, {
    title: "规格编码",
    key: "merchant_code",
    type: "text",
  }, {
    title: "数量",
    key: "skuNum",
    type: "text",
  }, {
    title: "金额",
    key: "skuPayment",
    type: "text",
  }, {
    title: "总数",
    key: "total_num",
    type: "text",
  }, {
    title: "总金额",
    key: "total_payment",
    type: "text",
  }, {
    title: "备注",
    key: "remark",
    type: "text",
  }
]

const originExportColumns = computed(() => {
  return groupType.value === 1 ? [...originGoodsExportColumns] : [...originProductExportColumns]
})

const defaultGoodsExportColumns = ['num_iid', 'goods_title', 'customTitle', 'goods_pic', 'outer_goods_id', 'sku_id', 'sku_value', 'customSkuValue',
  'sku_pic', 'outer_id', 'skuNum', 'skuPayment', 'total_num']

const defaultProductExportColumns = ['product_name', 'product_no', 'sku_name', 'image_url', 'merchant_code',
  'skuNum', 'skuPayment', 'total_num']

const exportColumns = ref([])
const checkAll = computed(() => {
  if (!exportColumns.value?.length) {
    return false
  }
  return exportColumns.value.length === originExportColumns.value.length
})

function exportCSV() {
  const config = groupType.value === 1 ? goodsPickingRef.value.getConfigForExport() : productPickingRef.value.getConfigForExport()
  if (!config?.list?.length) {
    return;
  }
  exportModal.value = true
}

function checkAllColumn(e) {
  if (e) {
    exportColumns.value = originExportColumns.value.map(it => it.key)
  } else {
    exportColumns.value = []
  }
  changeExportColumns()
}

function chooseNotClick() {
  if (exportColumns.value.length === originExportColumns.value.length) {
    exportColumns.value = []
  } else {
    exportColumns.value = originExportColumns.value.filter(it => !exportColumns.value.includes(it.key)).map(it => it.key)
  }
  changeExportColumns()
}

function changeExportColumns() {
  const config = {...dbExportConfig.value}
  if (groupType.value === 1) {
    config.goods = exportColumns.value
  } else {
    config.product = exportColumns.value
  }
  dbExportConfig.value = config
  localStorage.setItem('beihuo_export_set', JSON.stringify(config))
}

function generateRemark(remarkList) {
  return remarkList.map(it => {
    return `订单编号：${it.tid}，卖家备注：${it.sellerMemo}${it.buyerMessage ? '，买家留言：' + it.buyerMessage : ''}`
  }).join("；")
}

function commitExport() {
  const list = []
  let tableData = [],config = {}
  switch (groupType.value) {
    case 1:
      config = goodsPickingRef.value.getConfigForExport()
      tableData = config.list
      if (config.showType === 2) {
        //按照sku展示
        tableData.forEach(it => {
          const obj = {...it}
          obj.remark = generateRemark(it.remarkList)
          list.push(obj)
        })
      } else {
        //按商品展示
        tableData.forEach(it => {
          const obj = {...it}
          if (it.rowspan) {
            obj.remark = generateRemark(it.remarkList)
          } else {
            obj.num_iid = ''
            obj.goods_title = ''
            obj.customTitle = ''
            obj.goods_pic = ''
            obj.outer_goods_id = ''
            obj.total_num = ''
            obj.total_payment = ''
            obj.remark = ''
          }
          list.push(obj)
        })
      }
      break;
    case 2:
      config = productPickingRef.value.getConfigForExport()
      tableData = config.list
      if (config.showType === 2) {
        //按照sku展示
        tableData.forEach(it => {
          const obj = {...it}
          obj.remark = generateRemark(it.remarkList)
          list.push(obj)
        })
      } else {
        //按商品展示
        tableData.forEach(it => {
          const obj = {...it}
          if (it.rowspan) {
            obj.remark = generateRemark(it.remarkList)
          } else {
            obj.product_name = ''
            obj.product_no = ''
            obj.total_num = ''
            obj.total_payment = ''
            obj.remark = ''
          }
          list.push(obj)
        })
      }
  }
  let arr = originExportColumns.value.filter(it => exportColumns.value.includes(it.key))
  const tableColumnMap = {}
  arr.forEach(it => tableColumnMap[it.key] = it.title)
  //去除掉多余的字段
  const resList = []
  list.forEach(it => {
    const item = {}
    Object.keys(it).forEach(key => {
      if (exportColumns.value.includes(key)) {
        item[key] = it[key]
      }
    })
    resList.push(item)
  })
  const imageConfigs = arr.filter(it=>it.type === 'image').map(it=>{return {
    imageColumn: it.key,
    cellWidth: 50,
    cellHeight: 50
  }})
  exportJsonToExcelWithImages(resList, `备货单_${new Date().getTime()}`, tableColumnMap, imageConfigs)
  exportModal.value = false
}
</script>

<template>
  <div>
    <div class="order-picking-list">
      <el-radio-group v-model="groupType" @change="changeType">
        <el-radio-button v-for="it in typeList" :key="it.value" :value="it.value" :label="it.label"/>
      </el-radio-group>
      <goods-picking-list v-if="groupType === 1" ref="goodsPickingRef"/>
      <product-picking-list v-if="groupType === 2" ref="productPickingRef"/>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <div class="ml-5">
        <el-button type="primary" @click="printPickingList">打印备货单</el-button>
        <el-button @click="exportCSV">导出备货单</el-button>
      </div>
    </div>
    <el-dialog title="按配置导出" v-model="exportModal" :transfer="false" width="700px">
      <div class="flex_center">
        <el-checkbox :model-value="checkAll" @change="checkAllColumn">全选</el-checkbox>
        <a class="ml-2" @click="chooseNotClick">反选</a>
      </div>
      <el-divider style="margin:12px 0"/>
      <el-checkbox-group v-model="exportColumns" size="large" @change="changeExportColumns">
        <el-checkbox class="export-col" v-for="it in originExportColumns" :key="it.key" :value="it.key">{{ it.title }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="exportModal = false">取消</el-button>
        <el-button type="primary" @click="commitExport" :disabled="!exportColumns.length">确定导出</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss">
.order-picking-list {
  margin: 0.75rem;
}

.export-col {
  margin: 5px;
  width: 100px;
  max-width: 100px;
}

</style>
