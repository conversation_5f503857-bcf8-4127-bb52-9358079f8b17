<script setup>
import {bindPurchaseOrderSource, createPickTask, getPreparePurchaseOrders} from "~/base/api/order.js";
import {ElInput, ElMessageBox} from "element-plus";
import TimeRangeSelect from "~/base/views/components/timeRangeSelect.vue";
import {TYPE_LABEL} from "@/utils/commonData.js";
import {useMessage} from "@/hooks/useMessage.js";
import SelectGoods from "~/base/views/components/selectGoods.vue";
import {bindGoodProducts} from "~/base/api/goods.js";
import SourceSelect from "~/base/views/components/sourceSelect.vue";

const orderList = ref([])
const expandedRows = ref([])

const searchItem = [
  {
    label: '订单来源', prop: 'sourceId', render: (model) => {
      return h(SourceSelect, {
        modelValue: model.formData.sourceId,
        includeMerchant: true,
        'onUpdate:modelValue': (value) => {
          model.formData.sourceId = value;
        }
      });
    }
  },
  {
    label: '商品信息', prop: 'goods', render: (model) => {
      return h(ElInput, {
        placeholder: '商品名称/商品ID/商品编码/商品简称',
        modelValue: model.formData.goods,
        'onUpdate:modelValue': (value) => {
          model.formData.goods = value;
        }
      });
    }
  },
  {
    label: '规格信息', prop: 'sku', render: (model) => {
      return h(ElInput, {
        placeholder: '规格名称/规格简称/规格编码',
        modelValue: model.formData.sku,
        'onUpdate:modelValue': (value) => {
          model.formData.sku = value;
        }
      });
    }
  },
  {
    label: '时间筛选', prop: 'timeRange', render: (model) => {
      return h(TimeRangeSelect, {
        modelValue: model.formData.timeRange,
        source: 'not-send',
        originalTimeField: 'order_created_at',
        'onUpdate:modelValue': (value) => {
          model.formData.timeRange = value;
        }
      });
    }
  },
  {
    label: '地区筛选', prop: 'areaId', render: (model) => {
      return h(ElInput, {
        placeholder: '全部区域',
        modelValue: model.formData.areaId,
        'onUpdate:modelValue': (value) => {
          model.formData.areaId = value;
        }
      });
    }
  }
]
const DateFormat = window.DateFormat;

function getData(params) {
  let searchParam = {page: pageReq.value.page, page_size: pageReq.value.pageSize}
  if (params) {
    searchParam = {...searchParam, ...params}
  }
  if (params?.timeRange) {
    searchParam.timeField = params.timeRange.timeField
    const now = new Date();
    if (params.timeRange.timeRange?.length) {
      searchParam.begin_at = DateFormat.format.date(
        params.timeRange.timeRange[0] || new Date(Date.now() - 90 * 86400000),
        "yyyy-MM-dd HH:mm:ss"
      );
      searchParam.end_at = DateFormat.format.date(
        params.timeRange.timeRange[1] || new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
        "yyyy-MM-dd HH:mm:ss"
      );
      delete searchParam.timeRange
    }
  }
  getPreparePurchaseOrders(searchParam).then(res => {
    res.data.list.forEach(it => {
      it.sourceName = TYPE_LABEL[it.order.type]
      it.receiverInfo = it.order.receiver_name + ',' + it.order.receiver_phone + ','
        + it.order.receiver_province + it.order.receiver_city + it.order.receiver_district + (it.order.receiver_town || '') + it.order.receiver_address
    })
    orderList.value = res.data.list
    pageReq.value.total = res.data.total
  })
}
const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 1
})

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

const tableRef = ref()

const toggleExpand = (row) => {
  if (expandedRows.value.includes(row.id)) {
    expandedRows.value = expandedRows.value.filter(it => it !== row.id)
  } else {
    expandedRows.value.push(row.id)
  }
  tableRef.value.toggleRowExpansion(row);
};

const sortList = [
  {value: "order_created_time,desc", label: "下单时间从近到远", order: "desc"},
  {value: "order_created_time,asc", label: "下单时间从远到近", order: "asc"},
  {value: "pay_time,desc", label: "付款时间从近到远", order: "desc"},
  {value: "pay_time,asc", label: "付款时间从远到近", order: "asc"},
  {value: "promise_ship_at,desc", label: "剩余发货时间从小到大", order: "desc"},
  {value: "take_waybill_at,desc", label: "取号时间从近到远", order: "desc"},
  {value: "take_waybill_at,asc", label: "取号时间从远到近", order: "asc"},
  {value: "printed_time,desc", label: "打印时间从近到远", order: "desc"},
  {value: "printed_time,asc", label: "打印时间从远到近", order: "asc"},
  {value: "goods_count,desc", label: "商品数量从多到少", order: "desc"},
  {value: "goods_count,asc", label: "商品数量从少到多", order: "asc"},
  {value: "goods_price,desc", label: "商品金额从大到小", order: "desc"},
  {value: "goods_price,asc", label: "商品金额从小到大", order: "asc"},
  {value: "goods_title,asc", label: "商品名称", order: "asc"},
  {value: "custom_title,asc", label: "商品简称", order: "asc"},
  {value: "outer_iid,asc", label: "商品编码", order: "asc"},
  {value: "sku_value,asc", label: "规格名称", order: "asc"},
  {value: "custom_sku_value,asc", label: "规格简称", order: "asc"},
  {value: "outer_sku_iid,asc", label: "规格编码", order: "asc"},
  {value: "area,asc", label: "省份城市", order: "asc"}
]

const sortColumn = ref({
  field: 'order_created_time,desc',
  sort: 'desc'
})

const modalObj = ref({
  visible: false,
  title: '',
  width: '30%',
  form: {},
  type: '',
})

function asyncOk() {

}

function setAllocationRule() {
  modalObj.value = {
    visible: true,
    title: '自动采购设置',
    width: '30%',
    form: {},
    type: 'setAllocation',
  }
}


function setProductRule(){
  window.open('#/goods/source/relation')
}


const {
  getMenuCollapseState
} = useSettingStore()

const selectRowCount = computed(() => {
  if (!tableRef.value) {
    return 0;
  }
  return tableRef.value.getSelectionRows().length
})
const msg = useMessage()

async function handlePurchase() {
  if (!selectRowCount.value) {
    msg.error('请选择订单')
    return
  }
  const rows = tableRef.value.getSelectionRows()
  //判断是否存在未绑定货源的订单
  const notBindList = rows.filter(it=>!it.product_source_skus?.length)
  if (notBindList ?.length) {
    if (notBindList.length === rows.length) {
      //都没绑定，不用提示，直接出弹框
      const list = generateSourceProductList(notBindList)
      showBindRelation(list, list.findIndex(it=>it.assignee_id || (!it.sku_id && it.product_sku_id)) > -1)
    }else {
      ElMessageBox.confirm(`您选中的订单中存在未匹配货源的`, '温馨提示', {
        confirmButtonText: '匹配货源',
        cancelButtonText: '忽略未匹配的',
        distinguishCancelAndClose: true,
        type: 'warning',
      }).then(() => {
        //匹配货源
        const list = generateSourceProductList(notBindList)
        showBindRelation(list, list.findIndex(it=>it.assignee_id || (!it.sku_id && it.product_sku_id)) > -1)
      }).catch((action) => {
        //直接创建拿货任务
        if (action === 'cancel') {
          generateTask(rows.filter(it=>it.product_source_skus?.length))
        }
      })
    }
    return
  }
  //直接创建拿货任务
  generateTask(rows)
}
function generateTask(list) {
  console.log('生成拿货任务，',list)
  const params = []
  list.forEach(it=>{
    it.product_source_skus.forEach(item=>{
      params.push({
        "order_id": it.order_id,
        "order_item_id": it.id,
        "product_id": item.product_id,
        "product_sku_id": item.id,
        "pick_price": item.pivot?.pick_price
      })
    })
  })
  createPickTask({list: params}).then(()=>{
    msg.success('采购成功')
    modalObj.value.visible = false
    setTimeout(() => {
      getData()
    }, 1000)
  })
}
function generateSourceProductList(rows) {
  //按照skuId分组、筛选出自由订单、代发订单和平台单
  const skuMap = {}, productSkuMap = {}
  rows.forEach(it => {
    if (it.sku_id) {
      //商品
      const obj = skuMap[it.sku_id] || {idObj: [], ...it}
      delete obj.product_source_skus
      obj.idObj.push({order_item_id: it.id, order_id: it.order_id})
      if (!obj.productSkus?.length && it.product_source_skus?.length) {
        obj.productSkus = it.product_source_skus
      } else if (!obj.productSkus?.length) {
        obj.productSkus = []
      }
      skuMap[it.sku_id] = obj
    } else if (it.product_sku_id) {
      //货品
      const obj = productSkuMap[it.product_sku_id] || {orderItemIds: [], orderIds: [], ...it}
      delete obj.product_source_skus
      obj.orderItemIds.push(it.id)
      obj.orderIds.push(it.order_id)
      if (!obj.productSkus?.length && it.product_source_skus?.length) {
        obj.productSkus = it.product_source_skus
      } else if (!obj.productSkus?.length) {
        obj.productSkus = []
      }
      productSkuMap[it.sku_id] = obj
    }
  })
  const list = Object.values(skuMap)
  list.push(...Object.values(productSkuMap))
  return list;
}

function bindGoods(rows) {
  if (!rows?.length) {
    rows = tableRef.value.getSelectionRows()
  }
  if (!rows?.length) {
    msg.error('请选择订单')
    return
  }
  //判断是否有货品或代发的订单
  const hasSpecialOrders = rows.findIndex(it => it.assignee_id || (!it.sku_id && it.product_sku_id))
  const list = generateSourceProductList(rows)
  showBindRelation(list, hasSpecialOrders > -1)
}

function showBindRelation(list, temp) {
  if (!list.length) {
    return
  }
  list.forEach(it => it.change = false)
  modalObj.value = {
    type: 'bindRelation',
    visible: true,
    title: '关联货源',
    width: '70%',
    form: {list, temp},
  }
}

function onConfirm(temp) {
  const list = modalObj.value.form.list
  const changeList = list.filter(it => it.change)
  if (!changeList?.length) {
    msg.error('您未做任何修改')
    return;
  }
  const params = []
  if (temp) {
    changeList.forEach(it => {
      it.idObj.forEach(item => {
        it.productSkus.forEach(sku => {
          params.push({
            order_item_id: item.order_item_id,
            ...sku.pivot
          })
        })
      })
    })
    bindPurchaseOrderSource({list: params}).then(() => {
      msg.success('关联货源成功')
      modalObj.value.visible = false
      setTimeout(() => {
        getData()
      }, 1000)
    })
    return
  }
  //保存到全局
  changeList.forEach(it => {
    it.productSkus.forEach(sku => {
      params.push({
        bind_type: 2,
        goods_id: it.goods_sku.goods_id,
        goods_sku_id: it.goods_sku.id,
        ...sku.pivot
      })
    })
  })
  bindGoodProducts({list: params}).then(() => {
    msg.success('关联货源成功')
    modalObj.value.visible = false
    setTimeout(() => {
      getData()
    }, 1000)
  })
}

function cancelRelation(item, row) {
  row.productSkus = row.productSkus.filter(it => it.id !== item.id)
  row.change = true
}

const platformGoodsRef = ref()
const currentRow = ref({})

function chooseSource(row) {
  currentRow.value = row
  //排除掉已经选择的
  const factoryProductSkuIds = row.productSkus.map(it => it.product_sku_id || it.id)
  platformGoodsRef.value.init({factoryProductSkuIds}, false, 3, true)
}
function init(){
  pageReq.value.page = 1
  getData();
}

defineExpose({init});

function changeGoods(list) {
  if (!list.length) {
    return
  }
  const order = modalObj.value.form.list.find(it => it.id === currentRow.value.id)
  order.productSkus.push(...list.map(it => {
    return {
      ...it, pivot: {
        pick_price: it.selling_price,
        product_id: it.product_id,
        product_sku_id: it.id
      }
    }
  }))
  order.change = true
}

</script>

<template>
  <div>
    <div class="prepare-purchase">
      <div class="order-search">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <div class="order-opt-content flex_center">
          <div class="ml-auto flex_center">
            <a @click="setAllocationRule">采购设置</a>
            <el-select class="ml-8" style="width: 200px" v-model="sortColumn.field">
              <el-option v-for="it in sortList" :key="it.value" :label="it.label" :value="it.value"/>
            </el-select>
          </div>
        </div>
        <el-table :data="orderList" row-key="id" ref="tableRef" :expand-row-keys="expandedRows">
          <el-table-column width="40" type="selection"/>
          <el-table-column width="1" type="expand" class-name="hidden-expand-icon">
            <template #default="scope">
              <div class="expand-content">
                <el-card>
                  <template #header>
                    <div class="flex_center">
                      <span>订单号：</span>
                      <span>{{ scope.row.tid }}</span>
                      <span class="ml-5">下单时间：</span>
                      <span>{{ scope.row.order?.order_created_at }}</span>
                      <span class="ml-5">承诺揽收时间：</span>
                      <span>{{ scope.row.order?.promise_ship_at }}</span>
                    </div>
                  </template>
                  <div class="flex">
                    <div>
                      <div class="flex_top">
                        <div>
                          <p class="title-desc">商品信息</p>
                          <div class="flex_top mt-2">
                            <el-image style="width: 60px; height: 60px;min-width: 60px" :src="scope.row.sku_pic"
                                      :hide-on-click-modal="true" :preview-src-list="[scope.row.sku_pic]">
                              <template #error>
                                <div class="no-pic">暂无图片</div>
                              </template>
                            </el-image>
                            <div class="ml-3">
                              <p>{{ scope.row.goods_title }}</p>
                              <p>{{ scope.row.sku_value }}</p>
                              <p>¥{{ scope.row.sku_price }}
                                <span class="ml-2">X {{ scope.row.sku_num }}</span>
                              </p>
                            </div>
                          </div>
                        </div>
                        <div class="ml-5">
                          <p class="title-desc">货源信息</p>
                          <div class="bind-products mt-2">
                            <div v-for="it in scope.row.product_source_skus" class="mr-2 flex_top">
                              <el-image style="width: 60px; height: 60px;min-width: 60px" :src="it.image_url"
                                        :hide-on-click-modal="true" :preview-src-list="[it.image_url]">
                                <template #error>
                                  <div class="no-pic">暂无图片</div>
                                </template>
                              </el-image>
                              <div class="ml-3">
                                <p>{{ it.product?.name }}</p>
                                <p>{{ it.sku_name }}</p>
                                <p>¥{{ it.pivot?.pick_price }}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                      </div>
                    </div>
                    <div class="right_price flex_center">
                      <div class="red">¥ {{ scope.row.payment }}</div>
                    </div>
                  </div>
                </el-card>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标签" :show-overflow-tooltip="false">
            <template #default="scope"></template>
          </el-table-column>
          <el-table-column label="订单来源" :show-overflow-tooltip="false" prop="source">
            <template #default="scope">
              <p>【{{ scope.row.sourceName || '自定义' }}】</p>
              <p>{{ scope.row.shop_title?.shop_title }}</p>
            </template>
          </el-table-column>
          <el-table-column label="订单号" :show-overflow-tooltip="false" prop="tid"/>
          <el-table-column label="收件信息" :show-overflow-tooltip="false" prop="receiverInfo"/>
          <el-table-column label="留言备注" :show-overflow-tooltip="false" prop="remark">
            <template #default="scope">
              <p>留言: {{ scope.row.order?.buyer_message }}</p>
              <p>备注: {{ scope.row.order?.seller_memo }}</p>
            </template>
          </el-table-column>
          <el-table-column label="商品" :show-overflow-tooltip="false" min-width="200">
            <template #default="scope">
              <div class="flex_center">
                <el-image
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="scope.row.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[scope.row.sku_pic]"
                >
                  <template #error>
                    <div class="no-pic">暂无图片</div>
                  </template>
                </el-image>
                <div class="ml-2">
                  <p>{{ scope.row.goods_title }}</p>
                  <p>{{ scope.row.sku_value }}</p>
                  <p>X {{ scope.row.sku_num }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="下单时间/承诺揽收时间" :show-overflow-tooltip="false" width="170">
            <template #default="scope">
              <p>{{ scope.row.order.order_created_at }}</p>
              <p>{{ scope.row.order.promise_ship_at }}</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" :show-overflow-tooltip="false" width="80">
            <template #default="scope">
              <p @click="toggleExpand(scope.row)" class="opt-btn">
                {{ expandedRows.includes(scope.row.id) ? '收起' : '详情' }}
              </p>
              <p class="opt-btn" @click="bindGoods([scope.row])">关联货源</p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <div>
        <span>已选</span>
        <span class="order_count">{{ selectRowCount }}</span>
        <span>笔</span>
      </div>
      <div class="ml-10">
        <el-button type="primary" @click="handlePurchase">去采购</el-button>
        <el-button @click="bindGoods(null)">批量关联货源</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
      <template v-if="modalObj.type === 'setAllocation'">
        <div class="flex_center">
          <span>自动采购订单</span>
          <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
        </div>
        <p class="red mt-1">开启自动采购配后，符合条件的订单会自动生成拿货任务</p>
        <div class="flex_center mt-5">
          <!--          <el-checkbox >按商品关联的货源自动采购</el-checkbox>-->
          <span>按商品关联的货源自动采购</span>
          <a class="ml-2" @click="setProductRule">货源设置</a>
        </div>
      </template>
      <template v-else-if="modalObj.type === 'bindRelation'">
        <el-table :data="modalObj.form.list">
          <el-table-column label="商品规格">
            <template #default="scope">
              <div class="flex_center">
                <el-image
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="scope.row.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[scope.row.sku_pic]"
                >
                  <template #error>
                    <div class="no-pic">暂无图片</div>
                  </template>
                </el-image>
                <div class="ml-2">
                  <p>{{ scope.row.goods_title }}</p>
                  <p>{{ scope.row.sku_value }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="货源信息" align="center">
            <el-table-column label="已关联货源规格" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.productSkus" :key="idx" class="goods_line">
                  <div class="flex_top">
                    <el-image
                      style="width: 60px; height: 60px;min-width: 60px"
                      :src="item.image_url"
                      :hide-on-click-modal="true"
                      :preview-src-list="[item.image_url]"
                    />
                    <div class="ml-2">
                      <p>{{ item.sku_name }}</p>
                      <p>规格编码：{{ item.merchant_code || '无' }}</p>
                      <p>采购价：¥{{ item.pivot?.pick_price }}</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.productSkus" :key="idx" class="goods_line">
                  <a @click="cancelRelation(item,scope.row)">取消关联</a>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <a @click="chooseSource(scope.row)">选择货源</a>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #footer>
        <el-button @click="modalObj.visible = false">取 消</el-button>
        <el-button type="primary" @click="asyncOk" v-if="modalObj.type === 'setAllocation'">确 定</el-button>
        <el-button type="primary" plain @click="onConfirm(true)" v-if="modalObj.type === 'bindRelation'">仅保存到订单
        </el-button>
        <el-button type="primary" @click="onConfirm(false)"
                   v-if="modalObj.type === 'bindRelation' && !modalObj.form.temp">保存并同步到商品
        </el-button>
      </template>
    </el-dialog>
    <select-goods ref="platformGoodsRef" title="添加要关联的货源" @change="changeGoods"/>
  </div>
</template>


<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.no-pic {
  background: var(--el-fill-color-light);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: gray;
}

.title-desc {
  font-weight: bold;
}

.bind-products {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.prepare-purchase {
  margin: 0.75rem;

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .sort_item {
    cursor: pointer;
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    transition: all .5s;
    color: #ccc;
  }

  .sort_checked {
    color: #fff;
    border: none;
    background: var(--el-color-primary);
  }

  .order-search {
    background: white;
    padding: 0.75rem 0.75rem 0 0.75rem;
    margin: 0.75rem 0;
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}

.question-img {
  font-size: 20px;
  cursor: pointer;
}

.opt-btn {
  color: var(--el-color-primary);
  cursor: pointer;
}

.goods-column {
  .goods_line {
    height: 80px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
  }

  .goods_line:last-child {
    border-bottom: none;
  }
}
</style>
