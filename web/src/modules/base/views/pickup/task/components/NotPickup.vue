<script setup>
import {useMessage} from '@/hooks/useMessage.ts'
import {assignCargoUser, getHasPurchaseOrders} from "~/base/api/order.js";
import FactorySelect from "~/base/views/components/factorySelect.vue";
import TimeRangeSelect from "~/base/views/components/timeRangeSelect.vue";
import SourceSelect from "~/base/views/components/sourceSelect.vue";
import {PICK_UP_STATUS_MAP, TYPE_LABEL} from "@/utils/commonData.js";

const msg = useMessage()

const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 1
})

function init(){
  pageReq.value.page = 1
  getData();
}

defineExpose({init});

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

const searchItem = [
  {
    label: '订单来源', prop: 'sourceId', render: (model) => {
      return h(SourceSelect, {
        modelValue: model.formData.sourceId,
        includeMerchant: true,
        'onUpdate:modelValue': (value) => {
          model.formData.sourceId = value;
        }
      });
    }
  },
  {
    label: '时间筛选', prop: 'timeRange', render: (model) => {
      return h(TimeRangeSelect, {
        modelValue: model.formData.timeRange,
        source: 'not-send',
        originalTimeField: 'order_created_at',
        'onUpdate:modelValue': (value) => {
          model.formData.timeRange = value;
        }
      });
    }
  },
  {
    label: () => '订单搜索',
    prop: 'username',
    render: 'input',
  },
  {
    label: () => '指派状态',
    prop: 'status',
    render: 'select',
  },
  {
    label: () => '拿货员',
    prop: 'status1',
    render: 'select',
  },
  {
    label: () => '拿货状态',
    prop: 'status2',
    render: 'select',
  },
  {
    label: () => '厂家',
    prop: 'factoryId',
    render: (model) => {
      return h(FactorySelect, {
        modelValue: model.formData.factoryId,
        'onUpdate:modelValue': (value) => {
          model.formData.factoryId = value;
        }
      });
    }
  }
]

const orderList = ref([])
const tableRef = ref()

const shopList = useUserStore().getShops()
const merchantList = useUserStore().getMerchants()

function getData(params) {
  let searchParam = {page: pageReq.value.page, page_size: pageReq.value.pageSize}
  if (params) {
    searchParam = {...searchParam, ...params}
  }
  if (params?.timeRange) {
    searchParam.timeField = params.timeRange.timeField
    const now = new Date();
    if (params.timeRange.timeRange?.length) {
      searchParam.begin_at = DateFormat.format.date(
        params.timeRange.timeRange[0] || new Date(Date.now() - 90 * 86400000),
        "yyyy-MM-dd HH:mm:ss"
      );
      searchParam.end_at = DateFormat.format.date(
        params.timeRange.timeRange[1] || new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
        "yyyy-MM-dd HH:mm:ss"
      );
      delete searchParam.timeRange
    }
  }
  getHasPurchaseOrders(searchParam).then(res=> {
    res.data.list.forEach(it => {
      it.status = PICK_UP_STATUS_MAP[it.pickup_status]
      if (it.shop_id) {
        const shop = shopList.find(i => i.id === it.shop_id)
        if (shop && shop.type !== 99) {
          it.shopName = `【${TYPE_LABEL[shop.type]}】`+shop.shop_name
        } else if (shop && shop.type === 99) {
          it.shopName = '自定义店铺'
        }
      } else if (it.supplier_id) {
        it.shopName = merchantList.find(i => i.id === it.supplier_id)?.name
      }
      it.pickerName = it.picker?.nickname
    })
    orderList.value = res.data.list
    pageReq.value.total = res.data.total
  })
}

const {
  getMenuCollapseState
} = useSettingStore()

const selectRowCount = computed(() => {
  if (!tableRef.value) {
    return 0;
  }
  return tableRef.value.getSelectionRows().length
})

const modalObj = ref({
  visible: false,
  title: '',
  width: '30%',
  form: {},
  type: '',
})

function setPurchaseUserRule() {

}

const cargoUserList = useUserStore().getCargoUserList()

function bindCargoUser(list) {
  if (!list) {
    list = tableRef.value.getSelectionRows()
  }
  if (!list?.length) {
    msg.error('请选择订单')
    return
  }
  const pickerIds = [...new Set(list.filter(it=>it.picker_id).map(it=>it.picker_id))]
  modalObj.value = {
    type: 'bindCargoUser',
    visible: true,
    title: '指派拿货员',
    width: '300px',
    form: {list,picker_id: pickerIds?.length === 1 ? pickerIds[0] : null},
  }
}

function asyncOk(){
  if(modalObj.value.type === 'bindCargoUser') {
    if (!modalObj.value.form.picker_id) {
      msg.error('请选择拿货员')
      return
    }
    const list = modalObj.value.form.list.map(it=>{
      return {
        pick_task_id: it.id,
        picker_id: modalObj.value.form.picker_id
      }
    })
    assignCargoUser({list}).then(res=>{
      msg.success('指派成功')
      modalObj.value.visible = false
      setTimeout(() => {
        getData()
      }, 1000)
    })
  }
}

</script>

<template>
  <div>
    <div class="has-purchase">
      <div class="order-search">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <div class="order-opt-content flex_center">
          <div class="ml-auto flex_center">
            <a @click="setPurchaseUserRule">拿货员设置</a>
          </div>
        </div>
        <el-table :data="orderList" row-key="id" ref="tableRef">
          <el-table-column width="40" type="selection"/>
          <el-table-column label="店铺订单" :show-overflow-tooltip="false" width="190">
            <template #default="scope">
              <p>{{ scope.row.shopName }}</p>
              <p>{{ scope.row.order_item?.tid }}</p>
            </template>
          </el-table-column>
          <el-table-column label="拿货编码" :show-overflow-tooltip="false" width="120">
            <template #default="scope">
              <p v-for="it in scope.row.codes" :key="it.code">{{it.code}}</p>
            </template>
          </el-table-column>
          <el-table-column label="已拿件数/任务件数" :show-overflow-tooltip="false">
            <template #default="scope">
              <span>{{ scope.row.picked_quantity }} / {{ scope.row.total_quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="拿货状态" :show-overflow-tooltip="false" prop="status"/>
          <el-table-column label="拿货员" :show-overflow-tooltip="false" prop="pickerName"/>
          <el-table-column label="商品货号" :show-overflow-tooltip="false" min-width="200">
            <template #default="scope">
              <div class="flex_top">
                <el-image style="width: 60px; height: 60px;min-width: 60px" :src="scope.row.product_sku?.image_url"
                          :hide-on-click-modal="true" :preview-src-list="[scope.row.product_sku?.image_url]">
                  <template #error>
                    <div class="no-pic">暂无图片</div>
                  </template>
                </el-image>
                <div class="ml-3">
                  <p>{{ scope.row.product?.name }}</p>
                  <p>{{ scope.row.product_sku?.sku_name }}</p>
                  <p>¥{{ scope.row.pick_price }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" :show-overflow-tooltip="false" prop="created_at" width="180"/>
          <el-table-column label="操作" :show-overflow-tooltip="false" width="80">
            <template #default="scope">
              <p class="opt-btn" @click="bindCargoUser([scope.row])">指派</p>
              <p class="opt-btn">打印标签</p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <div>
        <span>已选</span>
        <span class="order_count">{{ selectRowCount }}</span>
        <span>笔</span>
      </div>
      <div class="ml-10">
        <el-button type="primary" @click="bindCargoUser(null)">指派拿货员</el-button>
        <el-button type="primary" plain>批量打印</el-button>
        <el-button type="danger" plain>批量停止拿货</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
      <template v-if="modalObj.type === 'bindCargoUser'">
        <el-select v-model="modalObj.form.picker_id" placeholder="请选择拿货员">
          <el-option v-for="it in cargoUserList" :key="it.id" :value="it.id" :label="it.nickname"/>
        </el-select>
      </template>
      <template #footer>
        <el-button @click="modalObj.visible = false">取 消</el-button>
        <el-button type="primary" @click="asyncOk" >确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.no-pic {
  background: var(--el-fill-color-light);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: gray;
}

.title-desc {
  font-weight: bold;
}

.bind-products {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.has-purchase {
  margin: 0.75rem;

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .sort_item {
    cursor: pointer;
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    transition: all .5s;
    color: #ccc;
  }

  .sort_checked {
    color: #fff;
    border: none;
    background: var(--el-color-primary);
  }

  .order-search {
    background: white;
    padding: 0.75rem 0.75rem 0 0.75rem;
    margin: 0.75rem 0;
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}

.question-img {
  font-size: 20px;
  cursor: pointer;
}

.opt-btn {
  color: var(--el-color-primary);
  cursor: pointer;
}

.goods-column {
  .goods_line {
    height: 80px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
  }

  .goods_line:last-child {
    border-bottom: none;
  }
}
</style>
