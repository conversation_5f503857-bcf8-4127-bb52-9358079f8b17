<script setup>
import {ElInput, ElOption, ElSelect} from 'element-plus';
import {useMessage} from "@/hooks/useMessage.js";
import {bindGoodProducts, deleteRelation, getGoodsList} from "~/base/api/goods.js";
import {TYPE_LABEL} from "@/utils/commonData.js";
import SelectGoods from "~/base/views/components/selectGoods.vue";

defineOptions({ name: 'source:goods:relation' })

const tableData = ref([])
const searchGoodsParams = ref({})
const pageReq = ref({
  page: 1,
  pageSize: 10,
  total: 0
})
const shopList = useUserStore().getShops()
const factoryList = useUserStore().getFactorys()
function getData(params) {
  tableData.value = []
  if (!params) {
    params = {...searchGoodsParams.value}
  } else {
    searchGoodsParams.value = {...params}
  }
  params.page = pageReq.value.page
  params.page_size = pageReq.value.pageSize
  params.is_with_product_source_skus = 1
  getGoodsList(params).then(res => {
    const {list, total} = res.data
    pageReq.value.total = total
    const resList = []
    list.forEach(it => {
      const shop = shopList.find(i => i.id === it.shop_id)
      it.shopName = shop?.shop_name
      it.platformName = TYPE_LABEL[it.type]
      it.skus.forEach((item, idx) => {
        //如果product_source_skus有值，按照user_id分组
        const product_source_skus = [],supplierMap = {}
        if (item.product_source_skus?.length) {
          item.product_source_skus.forEach(it2=>{
            let arr = [it2]
            if (supplierMap[it2.user_id]) {
              arr = supplierMap[it2.user_id]
              arr.push(it2)
            }
            supplierMap[it2.user_id] = arr
          })
          Object.keys(supplierMap).forEach(key=>{
            product_source_skus.push({supplier_id: key,supplierName: factoryList.find(i=>i.id==key)?.name,list:supplierMap[key]})
          })
        }
        const obj = {
          ...item,
          rowSpan: idx ? 0 : it.skus.length, ...it,
          product_source_skus,
          goods_id: item.goods_id,
          goods_sku_id: item.id
        }
        delete obj.skus
        resList.push(obj)
      })
    })
    tableData.value = resList
  })
}

const {
  getMenuCollapseState
} = useSettingStore()

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

const selectOptions = ref([
  {label: '全部', value: '-1'},
  {label: '已关联', value: '1'},
  {label: '未关联', value: '0'}
])
const searchItem = [
  {
    label: '店铺列表', prop: 'shopId', render: (model) => {
      return h(ElSelect, {
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        },
      })
    }
  }, {label: '商品名称', prop: 'productName', render: 'input'},
  {label: '商品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '商品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '商品编码/商品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },]

onMounted(() => {
  pageReq.value.page = 1
  getData()
})

function tableRowClassName({row, rowIndex}) {
  // 根据行数据返回类名
  if (rowIndex === 0) {
    return 'first-row-2';
  }
  return '';
}

function objectSpanMethod({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if (columnIndex === 0) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const msg = useMessage()

function addProductsRelation(item, row) {
  currentRow.value = row
  //选择当前的货源,同时排除掉已经选择的
  const factoryProductSkuIds = item.list.map(it=>it.product_sku_id||it.id)
  platformGoodsRef.value.init({supplierIds: [item.supplier_id],factoryProductSkuIds}, false, 3, true)
}
const currentRow = ref({})
const platformGoodsRef = ref()
function bindProducts(row) {
  currentRow.value = row
  //排除掉之前已经选择的货源
  const existSupplierId = row.product_source_skus.map(it=>it.supplier_id)
  const leftSupplierId = factoryList.filter(it=>!existSupplierId.includes(it.id))?.map(it=>it.id)
  platformGoodsRef.value.init({supplierIds: leftSupplierId}, false, 3, true)
}

function deleteProduct(item) {
  msg.delConfirm('取消关联后可以手动重新关联，确定取消关联吗？', '取消关联货品提醒').then(async () => {
    await deleteRelation(item.pivot)
    msg.success('取消关联成功')
    getData()
  })
}

function changeGoods(list){
  if (!list.length) {
    return
  }
  let params = list.map(it => {
    return {
      bind_type: 2,
      goods_id: currentRow.value.goods_id,
      goods_sku_id: currentRow.value.goods_sku_id,
      product_id: it.product_id,
      product_sku_id: it.product_sku_id,
      pick_price: it.selling_price
    }
  })
  bindGoodProducts({list: params}).then(() => {
    msg.success('关联成功')
    getData()
  })
}

</script>

<template>
  <div>
    <div class="goods-relation">
      <div class="tab-select">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <el-table :data="tableData" style="width: 100%"
                  :span-method="objectSpanMethod"
                  :header-row-class-name="tableRowClassName">

          <el-table-column label="平台商品" align="center">
            <el-table-column label="商品名称">
              <template #default="scope">
                <p>【{{scope.row.platformName}}】{{scope.row.shopName}}</p>
                <p>{{ scope.row.goods_title }}</p>
                <p>{{ scope.row.num_iid }}</p>
                <p>商品编码：{{ scope.row.outer_goods_id || '无' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="规格信息" min-width="120">
              <template #default="scope">
                <div class="flex_center">
                  <div class="flex_top">
                    <el-image
                      style="width: 60px; height: 60px;min-width: 60px"
                      :src="scope.row.sku_pic"
                      :hide-on-click-modal="true"
                      :preview-src-list="[scope.row.sku_pic]"
                    />
                    <div class="ml-2">
                      <p>{{ scope.row.sku_value }}</p>
                      <p>规格编码：{{ scope.row.outer_id || '无' }}</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="供应商货品">
            <el-table-column label="货品信息" class-name="goods-column" min-width="250">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.product_source_skus" :key="idx" class="goods_line">
                  <div style="margin-right:10px">{{item.supplierName}}</div>
                  <div class="flex_top product_item" v-for="(item1,idx1) in item.list" :key="idx+'_'+idx1" style="margin-right:10px">
                    <el-image
                      style="width: 40px; height: 40px;min-width: 40px"
                      :src="item1.image_url"
                      :hide-on-click-modal="true"
                      :preview-src-list="[item1.image_url]"
                    />
                    <div class="ml-2">
                      <p>{{ item1?.product.name }}</p>
                      <div class="flex_center">
                        <div>
                          <p>{{ item1.sku_name }}</p>
                          <p>规格编码：{{ item1.merchant_code || '无' }}</p>
                          <p>采购价：¥{{ item1.pivot?.pick_price }}</p>
                        </div>
                        <div class="ml-auto">
                          <ma-svg-icon name="material-symbols:auto-delete-outline" style="font-size:20px;color:red;cursor:pointer" @click="deleteProduct(item1)"/>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="继续添加" class-name="goods-column" width="80" align="center">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.product_source_skus" :key="idx" class="goods_line">
                  <a @click="addProductsRelation(item,scope.row)">新增</a>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <a @click="bindProducts(scope.row)">关联货源</a>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="order-footer flex_center"
           :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
        <div class="ml-auto">
          <el-pagination
            v-model:current-page="pageReq.page"
            v-model:page-size="pageReq.pageSize"
            :page-sizes="[10, 50, 100, 300]"
            layout="total, sizes, prev, pager, next"
            :total="pageReq.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <select-goods ref="platformGoodsRef" title="添加要关联的货源" @change="changeGoods"/>
  </div>

</template>

<style lang="scss">
.goods-relation {
  margin: 0.75rem;

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .order-table {
    background: white;
    padding: 0.75rem 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;
  }

  .goods-column {
    .goods_line {
      min-height: 80px;
      max-height: 120px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
    }

    .goods_line:last-child {
      border-bottom: none;
    }

    .product_item {
      border-right: 1px solid #ccc;
      padding-right: 10px;
    }
    .product_item:last-child {
      border-right: none;
      padding-right: 0;
    }
  }


  .first-row-1 {
    .el-table__cell:first-child {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:nth-child(2) {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }

  .first-row-2 {
    .el-table__cell:nth-child(2) {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:first-child {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }
}
</style>
