<script setup>

import {ElInput} from "element-plus";
import ShopSelect from "~/base/views/components/shopSelect.vue";
import FactorySelect from "~/base/views/components/factorySelect.vue";
import {getGoodsList, getPushGoodsList} from "~/base/api/goods.js";
import {TYPE_LABEL} from "@/utils/commonData.js";
import SourceSelect from "~/base/views/components/sourceSelect.vue";
import MerchatSelect from "~/base/views/components/merchatSelect.vue";

const searchItem = [
  {
    label: '店铺来源', prop: 'shopId', render: (model) => {
      return h(SourceSelect, {
        placeholder: '请选择店铺来源',
        includeSelf: false,
        includeMerchant: true,
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        }
      });
    }
  },
  {
    label: '代发厂家', prop: 'factoryId', render: () => FactorySelect
  },
  {
    label: '商品信息', prop: 'goods', render: (model) => {
      return h(ElInput, {
        placeholder: '商品名称/商品ID',
        modelValue: model.formData.goods,
        'onUpdate:modelValue': (value) => {
          model.formData.goods = value;
        }
      });
    }
  },
  {
    label: '规格信息', prop: 'sku', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.sku,
        'onUpdate:modelValue': (value) => {
          model.formData.sku = value;
        }
      });
    }
  },
  {
    label: '简称', prop: 'aliasName', render: (model) => {
      return h(ElInput, {
        placeholder: '商品简称/规格简称',
        modelValue: model.formData.aliasName,
        'onUpdate:modelValue': (value) => {
          model.formData.aliasName = value;
        }
      });
    }
  },
]
const searchItem2 = [
  {
    label: '商家列表', prop: 'merchantId', render: () => MerchatSelect
  },
  {
    label: '商品信息', prop: 'goods', render: (model) => {
      return h(ElInput, {
        placeholder: '商品名称/商品ID',
        modelValue: model.formData.goods,
        'onUpdate:modelValue': (value) => {
          model.formData.goods = value;
        }
      });
    }
  },
  {
    label: '规格信息', prop: 'sku', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.sku,
        'onUpdate:modelValue': (value) => {
          model.formData.sku = value;
        }
      });
    }
  },
]
const searchParams = ref({})
const searchPushParams = ref({})
const shopList = useUserStore().getShops()
function getData(params) {
  tableList.value = []
  if (activeMode.value === 1) {
    if (!params) {
      params = {...searchParams.value}
    } else {
      searchParams.value = {...params}
    }
    params.page = pageReq.value.page
    params.page_size = pageReq.value.pageSize
    getGoodsList(params).then(res => {
      const {list, total} = res.data
      pageReq.value.total = total
      const resList = []
      list.forEach(it => {
        const shop = shopList.find(i=>i.id === it.shop_id)
        it.shopName = shop?.shop_name
        it.platformName = TYPE_LABEL[it.type]
        it.skus.forEach((item, idx) => {
          const obj = {...item, rowSpan: idx ? 0 : it.skus.length, ...it, skuChecked: false}
          delete obj.skus
          resList.push(obj)
        })
      })
      tableList.value = resList
    })
    return
  }
  if (!params) {
    params = {...searchPushParams.value}
  } else {
    searchPushParams.value = {...params}
  }
  params.page = pageReq.value.page
  params.page_size = pageReq.value.pageSize
  getPushGoodsList(params).then(res=>{
    const {list, total} = res.data
    pageReq.value.total = total
    const resList = []
    list.forEach(it => {
      it.merchantName = it.shop?.bind_users[0]?.nickname
      it.skus.forEach((item, idx) => {
        const obj = {...item, rowSpan: idx ? 0 : it.skus.length, ...it, skuChecked: false}
        delete obj.skus
        resList.push(obj)
      })
    })
    tableList.value = resList
  })

}

onMounted(async () => {
  getData();
})
const tableList = ref([])

function objectSpanMethod({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if ([0, 1, 2, 3].includes(columnIndex)) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const pageReq = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

const {
  getMenuCollapseState
} = useSettingStore()


function editGoodsAlias(item) {

}

function editSkuAlias(item){

}

function selectionChange(e){
  const ids = e.map(it=>it.id)
  tableList.value.forEach(it=>{

  })
}

const activeMode = ref(1)
function changeMode() {
  pageReq.value.page = 1
  getData()
}


</script>


<template>
  <div class="goods-content">
    <div class="search-content">
      <el-tabs v-model="activeMode" @tab-change="changeMode">
        <el-tab-pane label="平台店铺商品" :name="1">
          <maSearch
            :options="{ showButton: true }"
            :form-options="{ labelWidth: '80px'}"
            :search-items="searchItem"
            @search="getData"
          />
        </el-tab-pane>
        <el-tab-pane label="商家推单商品" :name="2">
          <maSearch
            :options="{ showButton: true }"
            :form-options="{ labelWidth: '80px'}"
            :search-items="searchItem2"
            @search="getData"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="order-table">
      <el-table :data="tableList" :span-method="objectSpanMethod" @selection-change="selectionChange">
        <el-table-column type="selection" v-if="activeMode === 1"/>
        <el-table-column label="平台" prop="platformName" width="90" v-if="activeMode === 1"/>
        <el-table-column label="店铺" prop="shopName" width="120" v-if="activeMode === 1"/>
        <el-table-column label="来源商家" prop="merchantName" v-if="activeMode === 2"/>
        <el-table-column label="商品信息">
          <template #default="scope">
            <p>{{ scope.row.goods_title }}</p>
            <p>{{ scope.row.num_iid }}</p>
            <p>商品编码：{{ scope.row.outer_goods_id || '无' }}</p>
            <p class="flex_center">简称：{{scope.row.custom_title || '无'}}
              <ma-svg-icon v-if="activeMode === 1" name="material-symbols:edit-square-outline" size="20" class="ml-2 blue" @click="editGoodsAlias(scope.row)"/>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="规格信息">
          <template #default="scope">
            <div class="flex_center">
              <el-checkbox v-model="scope.row.skuChecked" v-if="activeMode === 1"/>
              <div class="flex_top ml-2">
                <el-image
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="scope.row.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[scope.row.sku_pic]"
                />
                <div class="ml-2">
                  <p>{{ scope.row.sku_value }}</p>
                  <p>规格编码：{{ scope.row.outer_id || '无' }}</p>
                  <p class="flex_center">简称：{{scope.row.custom_sku_value || '无'}}
                    <ma-svg-icon v-if="activeMode === 1" name="material-symbols:edit-square-outline" size="20" class="ml-2 blue" @click="editSkuAlias(scope.row)"/>
                  </p>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="重量(kg)" width="150" v-if="activeMode === 1">
          <template #default="scope">
            <el-input v-model="scope.row.weight" placeholder="重量">
              <template #append>kg</template>
            </el-input>
<!--            <el-input v-model="scope.row.volume" placeholder="体积" class="mt-2">-->
<!--              <template #append>m³</template>-->
<!--            </el-input>-->
          </template>
        </el-table-column>
        <el-table-column label="代发厂家" width="150" v-if="activeMode === 1">
          <template #default="scope">
            <factory-select v-model="scope.row.factoryId"/>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <!--        <div>-->
      <!--          <span>已选</span>-->
      <!--          <span class="order_count">1</span>-->
      <!--          <span>项</span>-->
      <!--        </div>-->
      <div v-if="activeMode === 1">
        <el-button type="primary">保存当前页修改</el-button>
        <el-button type="primary" plain>批量设置代发厂家</el-button>
        <el-button>批量设置重量</el-button>
<!--        <el-button>批量设置体积</el-button>-->
        <el-button>批量设置商品简称</el-button>
        <el-button>批量设置规格简称</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[10, 100, 200, 300]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

  </div>
</template>


<style scoped lang="scss">
.goods-content {
  margin: 0.75rem;

  .search-content {
    padding: 0.75rem;
    background: white;
  }

  .order-table {
    background: white;
    padding: 0.75rem 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;
  }
}

</style>
