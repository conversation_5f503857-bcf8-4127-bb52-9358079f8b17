<script setup>
import {ElInput, ElOption, ElSelect} from 'element-plus';
import {useMessage} from "@/hooks/useMessage.js";
import SelectGoods from "~/base/views/components/selectGoods.vue";
import {bindGoodProducts, deleteRelation, getGoodsList, getProducts, getPushGoodsList} from "~/base/api/goods.js";
import {TYPE_LABEL} from "@/utils/commonData.js";
import SourceSelect from "~/base/views/components/sourceSelect.vue";

defineOptions({name: 'goods:relation'})

const searchProductsParams = ref({})
const searchGoodsParams = ref({})
const searchMerchatGoodsParams = ref({})

const pageReq = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const {
  getMenuCollapseState
} = useSettingStore()

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

const activeMode = ref(1)
const tableData = ref([])

onMounted(() => {
  changeMode()
})

function changeMode() {
  pageReq.value.page = 1
  getData()
}

function getData(params) {
  tableData.value = []
  if (activeMode.value === 1) {
    //product
    if (!params) {
      params = {...searchProductsParams.value}
    } else {
      searchProductsParams.value = {...params}
    }
    params.page = pageReq.value.page
    params.page_size = pageReq.value.pageSize
    params.is_with_goods_skus = 1
    getProducts(params).then(res => {
      const {list, total} = res.data
      pageReq.value.total = total
      const resList = []
      list.forEach(it => {
        it.skus.forEach((item, idx) => {
          const obj = {
            ...item,
            rowSpan: idx ? 0 : it.skus.length, ...it,
            goods_skus: item.goods_skus || [],
            product_id: item.product_id,
            product_sku_id: item.id
          }
          if (obj.goods_skus.length) {
            obj.goods_skus.forEach(item => {
              const shop = shopList.find(i => i.id === item.shop_id)
              item.shopName = shop?.shop_name
              item.platformName = TYPE_LABEL[item.type]
            })
          }
          delete obj.skus
          resList.push(obj)
        })
      })
      tableData.value = resList
    })
    return
  }
  if (activeMode.value === 2) {
    //平台goods
    if (!params) {
      params = {...searchGoodsParams.value}
    } else {
      searchGoodsParams.value = {...params}
    }
    params.page = pageReq.value.page
    params.page_size = pageReq.value.pageSize
    params.is_with_product_skus = 1
    getGoodsList(params).then(res => {
      const {list, total} = res.data
      pageReq.value.total = total
      const resList = []
      list.forEach(it => {
        const shop = shopList.find(i => i.id === it.shop_id)
        it.shopName = shop?.shop_name
        it.platformName = TYPE_LABEL[it.type]
        it.skus.forEach((item, idx) => {
          const obj = {
            ...item,
            rowSpan: idx ? 0 : it.skus.length, ...it,
            product_skus: item.product_skus || [],
            goods_id: item.goods_id,
            goods_sku_id: item.id
          }
          delete obj.skus
          resList.push(obj)
        })
      })
      tableData.value = resList
    })
    return
  }
  //推单goods
  if (!params) {
    params = {...searchMerchatGoodsParams.value}
  } else {
    searchMerchatGoodsParams.value = {...params}
  }
  params.page = pageReq.value.page
  params.page_size = pageReq.value.pageSize
  params.is_with_product_skus = 1
  getPushGoodsList(params).then(res => {
    const {list, total} = res.data
    pageReq.value.total = total
    const resList = []
    list.forEach(it => {
      const shop = shopList.find(i => i.id === it.shop_id)
      it.merchantName = it.shop?.bind_users[0]?.nickname
      it.skus.forEach((item, idx) => {
        const obj = {
          ...item,
          rowSpan: idx ? 0 : it.skus.length, ...it,
          product_skus: item.product_skus || [],
          goods_id: item.goods_id,
          goods_sku_id: item.id
        }
        delete obj.skus
        resList.push(obj)
      })
    })
    tableData.value = resList
  })
}

const selectOptions = ref([
  {label: '全部', value: '-1'},
  {label: '已关联', value: '1'},
  {label: '未关联', value: '0'}
])
const searchItem = [
  {label: '货品名称', prop: 'productName', render: 'input'},
  {label: '货品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '货品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },
]

const searchItem2 = [
  {
    label: '店铺来源', prop: 'shopId', render: (model) => {
      return h(SourceSelect, {
        placeholder: '请选择店铺来源',
        includeSelf: false,
        includeMerchant: false,
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        }
      });
    }
  }, {label: '商品名称', prop: 'productName', render: 'input'},
  {label: '商品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '商品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '商品编码/商品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },]

const searchItem3 = [
  {
    label: '店铺来源', prop: 'shopId', render: (model) => {
      return h(SourceSelect, {
        placeholder: '请选择店铺来源',
        includePlatformShop: false,
        includeSelf: false,
        includeMerchant: true,
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        }
      });
    }
  }, {label: '商品名称', prop: 'productName', render: 'input'},
  {label: '商品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '商品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '商品编码/商品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },]

function tableRowClassName({row, rowIndex}) {
  // 根据行数据返回类名
  if (rowIndex === 0) {
    return activeMode.value === 1 ? 'first-row-1' : 'first-row-2';
  }
  return '';
}

function objectSpanMethod({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if (columnIndex === 0) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const msg = useMessage()

function cancelGoodsRelation(item, row) {
  msg.delConfirm('取消关联后可以手动重新关联，确定取消关联吗？', '取消关联商品提醒').then(async () => {
    await deleteRelation(item.pivot)
    msg.success('取消关联成功')
    getData()
  })
}

function cancelProductsRelation(item, row) {
  msg.delConfirm('取消关联后可以手动重新关联，确定取消关联吗？', '取消关联货品提醒').then(async () => {
    await deleteRelation(item.pivot)
    msg.success('取消关联成功')
    getData()
  })
}

const currentRow = ref({})
const title = ref('')
function bindGoods(row) {
  currentRow.value = row
  const skuIds = row.goods_skus.map(it=>it.sku_id)
  title.value = '添加要关联的平台商品'
  platformGoodsRef.value.init({skuIds, selfProductSkuIds: []}, false, 1)
}

function bindProducts(row) {
  currentRow.value = row
  title.value = '添加要关联的系统货品'
  const selfProductSkuIds = row.product_skus.map(it=>it.product_sku_id||it.id)
  platformGoodsRef.value.init({skuIds: [], selfProductSkuIds}, false, 2)
}

const platformGoodsRef = ref()

const shopList = useUserStore().getShops()

function changeGoods(list) {
  if (!list?.length) {
    return
  }
  let params = []
  if (activeMode.value === 1) {
    //选择的是货品

    params = list.map(it => {
      return {
        bind_type: 1,
        goods_id: it.goods_id,
        goods_sku_id: it.goods_sku_id,
        product_id: currentRow.value.product_id,
        product_sku_id: currentRow.value.product_sku_id
      }
    })

  } else {
    //选择的是商品
    params = list.map(it => {
      return {
        bind_type: 1,
        goods_id: currentRow.value.goods_id,
        goods_sku_id: currentRow.value.goods_sku_id,
        product_id: it.product_id,
        product_sku_id: it.product_sku_id
      }
    })
  }
  bindGoodProducts({list: params}).then(() => {
    msg.success('关联成功')
    if (activeMode.value === 1) {
      currentRow.value.goods_skus.push(...list)
    } else {
      currentRow.value.product_skus.push(...list)
    }
  })
}

</script>

<template>
  <div>
    <div class="goods-relation">
      <div class="tab-select">
        <el-tabs v-model="activeMode" @tab-change="changeMode">
          <el-tab-pane label="货品视角" :name="1">
            <maSearch
              :options="{ showButton: true }"
              :form-options="{ labelWidth: '80px'}"
              :search-items="searchItem"
              @search="getData"
            />
          </el-tab-pane>
          <el-tab-pane label="平台商品视角" :name="2">
            <maSearch
              :options="{ showButton: true }"
              :form-options="{ labelWidth: '80px'}"
              :search-items="searchItem2"
              @search="getData"
            />
          </el-tab-pane>
          <el-tab-pane label="推单商品视角" :name="3">
            <maSearch
              :options="{ showButton: true }"
              :form-options="{ labelWidth: '80px'}"
              :search-items="searchItem3"
              @search="getData"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="order-table">
        <el-table :data="tableData" style="width: 100%"
                  :span-method="objectSpanMethod"
                  :header-row-class-name="tableRowClassName">
          <template v-if="activeMode === 1">
            <el-table-column align="center" label="系统货品">
              <el-table-column label="货品名称/编码">
                <template #default="scope">
                  <p>{{ scope.row.name }} </p>
                  <p>货品编码：{{ scope.row.product_no || '无' }}</p>
                </template>
              </el-table-column>
              <el-table-column label="货品规格/编码/条码">
                <template #default="scope">
                  <div class="flex_top">
                    <el-image
                      style="width: 60px; height: 60px;min-width: 60px"
                      :src="scope.row.image_url"
                      :hide-on-click-modal="true"
                      :preview-src-list="[scope.row.image_url]"
                    />
                    <div class="ml-2">
                      <p>{{ scope.row.sku_name }}</p>
                      <p>规格编码：{{ scope.row.merchant_code || '无' }}</p>
                      <p>条形码：{{ scope.row.barcode || '无' }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="平台商品" align="center">
              <el-table-column label="商品名称" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.goods_skus" :key="idx" class="goods_line">
                    <div>
                      <p>{{ item.goods_title }}</p>
                      <p>{{ item.num_iid }}</p>
                      <p>商品编码：{{ item.outer_goods_id || '无' }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="规格信息" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.goods_skus" :key="idx" class="goods_line">
                    <div class="flex_top">
                      <el-image
                        style="width: 60px; height: 60px;min-width: 60px"
                        :src="item.sku_pic"
                        :hide-on-click-modal="true"
                        :preview-src-list="[item.sku_pic]"
                      />
                      <div class="ml-2">
                        <p>{{ item.sku_value }}</p>
                        <p>规格编码：{{ item.outer_id || '无' }}</p>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="店铺" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.goods_skus" :key="idx" class="goods_line">
                    <span>【{{ item.platformName }}】</span>
                    <span>{{ item.shopName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.goods_skus" :key="idx" class="goods_line">
                    <a @click="cancelGoodsRelation(item,scope.row)">取消关联</a>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <a @click="bindGoods(scope.row)">关联平台商品</a>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeMode === 2">
            <el-table-column label="平台商品" align="center">
              <el-table-column label="店铺/商品名称">
                <template #default="scope">
                  <p>【{{scope.row.platformName}}】{{scope.row.shopName}}</p>
                  <p>{{ scope.row.goods_title }}</p>
                  <p>{{ scope.row.num_iid }}</p>
                  <p>商品编码：{{ scope.row.outer_goods_id || '无' }}</p>
                </template>
              </el-table-column>
              <el-table-column label="规格信息">
                <template #default="scope">
                  <div class="flex_center">
                    <div class="flex_top">
                      <el-image
                        style="width: 60px; height: 60px;min-width: 60px"
                        :src="scope.row.sku_pic"
                        :hide-on-click-modal="true"
                        :preview-src-list="[scope.row.sku_pic]"
                      />
                      <div class="ml-2">
                        <p>{{ scope.row.sku_value }}</p>
                        <p>规格编码：{{ scope.row.outer_id || '无' }}</p>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="系统货品">
              <el-table-column label="货品名称/编码" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <div>
                      <p>{{ item.product?.name }}</p>
                      <p>货品编码：{{ item.product?.product_no || '无' }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="货品规格/编码/条码" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <div class="flex_top">
                      <el-image
                        style="width: 60px; height: 60px;min-width: 60px"
                        :src="item.image_url"
                        :hide-on-click-modal="true"
                        :preview-src-list="[item.image_url]"
                      />
                      <div class="ml-2">
                        <p>{{ item.sku_name }}</p>
                        <p>规格编码：{{ item.merchant_code || '无' }}</p>
                        <p>条形码：{{ item.barcode || '无' }}</p>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <a @click="cancelProductsRelation(item,scope.row)">取消关联</a>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <a @click="bindProducts(scope.row)">关联货品</a>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column label="推单商品" align="center">
              <el-table-column label="商家/商品名称">
                <template #default="scope">
                  <p>{{scope.row.merchantName}}</p>
                  <p>{{ scope.row.goods_title }}</p>
                  <p>{{ scope.row.num_iid }}</p>
                  <p>商品编码：{{ scope.row.outer_goods_id || '无' }}</p>
                </template>
              </el-table-column>
              <el-table-column label="规格信息">
                <template #default="scope">
                  <div class="flex_center">
                    <div class="flex_top">
                      <el-image
                        style="width: 60px; height: 60px;min-width: 60px"
                        :src="scope.row.sku_pic"
                        :hide-on-click-modal="true"
                        :preview-src-list="[scope.row.sku_pic]"
                      />
                      <div class="ml-2">
                        <p>{{ scope.row.sku_value }}</p>
                        <p>规格编码：{{ scope.row.outer_id || '无' }}</p>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="系统货品">
              <el-table-column label="货品名称/编码" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <div>
                      <p>{{ item.product?.name }}</p>
                      <p>货品编码：{{ item.product?.product_no || '无' }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="货品规格/编码/条码" class-name="goods-column">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <div class="flex_top">
                      <el-image
                        style="width: 60px; height: 60px;min-width: 60px"
                        :src="item.image_url"
                        :hide-on-click-modal="true"
                        :preview-src-list="[item.image_url]"
                      />
                      <div class="ml-2">
                        <p>{{ item.sku_name }}</p>
                        <p>规格编码：{{ item.merchant_code || '无' }}</p>
                        <p>条形码：{{ item.barcode || '无' }}</p>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
                <template #default="scope">
                  <div v-for="(item,idx) in scope.row.product_skus" :key="idx" class="goods_line">
                    <a @click="cancelProductsRelation(item,scope.row)">取消关联</a>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <a @click="bindProducts(scope.row)">关联货品</a>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <div class="order-footer flex_center"
           :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
        <div class="ml-auto">
          <el-pagination
            v-model:current-page="pageReq.page"
            v-model:page-size="pageReq.pageSize"
            :page-sizes="[10, 50, 100, 300]"
            layout="total, sizes, prev, pager, next"
            :total="pageReq.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <select-goods ref="platformGoodsRef" :title="title" @change="changeGoods"/>
  </div>
</template>

<style lang="scss">
.goods-relation {
  margin: 0.75rem;

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .order-table {
    background: white;
    padding: 0.75rem 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;
  }

  .goods-column {
    .goods_line {
      height: 80px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
    }

    .goods_line:last-child {
      border-bottom: none;
    }
  }


  .first-row-1 {
    .el-table__cell:first-child {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:nth-child(2) {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }

  .first-row-2 {
    .el-table__cell:nth-child(2) {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:first-child {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }
}
</style>
