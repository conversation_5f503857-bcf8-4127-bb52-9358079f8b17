<?php

namespace App\Http\Admin\Request\Goods;

use App\Http\Admin\Request\BaseFormRequest;
use Hyperf\Swagger\Annotation\Items;
use Hyperf\Swagger\Annotation\Property;
use Hyperf\Swagger\Annotation\Schema;

#[Schema(
    title: '批量创建商品货品关联',
    properties: [
        new Property(
            'list', 
            description: '商品货品关联数组', 
            type: 'array', 
            items: new Items(
                properties: [
                    new Property('bind_type', description: '绑定类型 1商品关联 2货源关联 3推单商品', type: 'int', example: 1),
                    new Property('goods_id', description: '商品id', type: 'int', example: 1),
                    new Property('goods_sku_id', description: '商品规格id', type: 'int', example: 1),
                    new Property('product_id', description: '货品id', type: 'int', example: 1),
                    new Property('product_sku_id', description: '货品规格id', type: 'int', example: 1),
                    new Property('pick_price', description: '拿货价', type: 'number', example: 100.00),
                ],
                type: 'object'
            )
        ),
    ]
)]
class GoodsProductRelationBatchRequest extends BaseFormRequest
{
    /** @var array */
    public array $list = [];

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'list' => 'required|array|min:1',
            'list.*.bind_type' => 'required|integer|in:1,2,3',
            'list.*.goods_id' => 'required|integer',
            'list.*.goods_sku_id' => 'required|integer',
            'list.*.product_id' => 'required|integer',
            'list.*.product_sku_id' => 'required|integer',
            'list.*.pick_price' => 'numeric|min:0',
        ];
    }

    public function attributes(): array
    {
        return [
            'list' => '商品货品关联数组',
            'list.*.bind_type' => '绑定类型',
            'list.*.goods_id' => '商品id',
            'list.*.goods_sku_id' => '商品规格id',
            'list.*.product_id' => '货品id',
            'list.*.product_sku_id' => '货品规格id',
        ];
    }
}