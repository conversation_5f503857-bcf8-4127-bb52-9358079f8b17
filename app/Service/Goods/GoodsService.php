<?php

namespace App\Service\Goods;

use App\Components\Facade4Api\ServiceFactory;
use App\Model\Order\OrderItem;
use App\Model\Order\OrderItemRelation;
use App\Model\Permission\User;
use App\Repository\Shop\ShopRepository;
use App\Service\IService;
use App\Repository\Goods\GoodsRepository as Repository;
use Hyperf\Collection\Arr;
use Hyperf\Database\Model\Builder;


class GoodsService extends IService
{
    public function __construct(
        protected readonly Repository $repository,
        protected readonly ShopRepository $shopRepository
    ) {}

    public function getRepository(): Repository
    {
        return $this->repository;
    }

    public function sync(User $user,array $shopIdArr)
    {

    }

    public function pageList(User $user, array $requestData, int $getCurrentPage, int $getPageSize)
    {
        $requestData['user_id'] = $user->id;

        $shopIds = $this->getBindShopIds($user);
//        $shopIds2 = $this->getMerchantShopIds($user);
//        return [
//            'shopIds' => $shopIds,
//            'shopIds2' => $shopIds2,
//        ];
        $excluded_sku_ids = $requestData['excluded_sku_ids'] ?? [];
        $is_with_product_skus = Arr::get($requestData, 'is_with_product_skus');
        $is_with_product_source_skus = Arr::get($requestData, 'is_with_product_source_skus');

        $requestData['shop_ids'] = $shopIds;
        $with = ['skus'];
        if (!empty($excluded_sku_ids)){
            $with = ['skus' => function ($query) use ($excluded_sku_ids) {
                $query->whereNotIn('sku_id', $excluded_sku_ids);
            },];
        }
        if ($is_with_product_skus){
            $with[] = 'skus.productSkus.product:id,name,product_no';
        }
        if ($is_with_product_source_skus){
            $with[] = 'skus.productSourceSkus.product:id,name,product_no';
        }
        return $this->repository->pageWith($requestData,$with, $getCurrentPage, $getPageSize);
    }
    
    /**
     * 商家推送商品列表
     *
     * @param User $user 当前用户
     * @param int $currentPage 当前页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function merchantPushList(User $user, int $currentPage, int $pageSize): array
    {
        $shopIds = $this->getMerchantShopIds($user);
        $skuIds = OrderItem::query()
            ->join('order_item_relations', 'order_items.id', '=', 'order_item_relations.order_item_id')
            ->where('order_item_relations.user_id', $user->id)
            ->where('order_item_relations.source_type', OrderItemRelation::SOURCE_TYPE_SUPPLIER)
            ->get()
            ->pluck('sku_id')
            ->unique()
            ->toArray();

        // 根据 sku_id 查询商品信息
        $requestData = [
            'shop_ids' => $shopIds,
            'sku_ids' => $skuIds,
        ];
        $query = $this->repository->getQuery();
        $with = ['shop:id,shop_name','shop.bindUsers:nickname','skus' => function ($query) use ($skuIds) {
            $query->whereIn('sku_id', $skuIds);
        }];
        $query->with($with);
        $query->wherehas('skus', function ($query) use ($skuIds) {
            $query->whereIn('sku_id', $skuIds);
        });

        return $this->repository->pageByQuery($query, $currentPage, $pageSize);
    }

}