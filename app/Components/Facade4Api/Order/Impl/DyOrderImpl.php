<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/9/15
 * Time: 19:00
 */

namespace App\Components\Facade4Api\Order\Impl;

use App\Components\Facade4Api\Client\CurlResult;
use App\Components\Facade4Api\Client\DyClient;
use App\Constants\PlatformConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Order\OrderServiceAbstract;
use App\Components\Facade4Api\Order\Request\FaPackDeliverRequest;
use App\Components\Facade4Api\Order\Request\OrderListRequest;
use App\Components\Facade4Api\Order\Response\CommonResponse;
use App\Components\Facade4Api\Order\Response\DecryptResponse;
use App\Components\Facade4Api\Order\Response\OrderListResponse;
use App\Components\Facade4Api\Order\Response\PackDeliverResponse;
use App\Constants\OrderConst;
use App\Model\Shop\Shop;
use App\Utils\Log;
use App\Utils\MathUtil;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Utils\Arr;
use InvalidArgumentException;

class DyOrderImpl extends OrderServiceAbstract
{
    protected array $orderStatusMap = [
        0 => OrderConst::ORDER_STATUS_UNKNOWN,
        1 => OrderConst::ORDER_STATUS_PADDING,
        2 => OrderConst::ORDER_STATUS_PAYMENT,
        3 => OrderConst::ORDER_STATUS_DELIVERED,
        4 => OrderConst::ORDER_STATUS_CLOSED,
        5 => OrderConst::ORDER_STATUS_SUCCESS,
        101 => OrderConst::ORDER_STATUS_PART_DELIVERED,
    ];

    protected array $refundStatusMap = [
        0 => OrderConst::REFUND_STATUS_NO,//售后初始化
        6 => OrderConst::REFUND_STATUS_YES, // (退货) 退货中-用户申请
        7 => OrderConst::REFUND_STATUS_YES, //售后退货中
        27 => OrderConst::REFUND_STATUS_NO, //拒绝售后申请
        12 => OrderConst::REFUND_STATUS_YES, //售后成功
        28 => OrderConst::REFUND_STATUS_NO, //售后失败(取消售后)
        11 => OrderConst::REFUND_STATUS_YES, //售后已发货
        29 => OrderConst::REFUND_STATUS_YES, //退货后拒绝退款
        13 => OrderConst::REFUND_STATUS_YES, //售后换货商家发货
        14 => OrderConst::REFUND_STATUS_YES, //售后换货用户收货
        51 => OrderConst::REFUND_STATUS_NO, //取消成功
        53 => OrderConst::REFUND_STATUS_YES, //逆向交易完成
    ];

    protected array $orderFlagMap = [
        0 => OrderConst::FLAG_GRAY,
        1 => OrderConst::FLAG_PURPLE,
        2 => OrderConst::FLAG_CYAN,
        3 => OrderConst::FLAG_GREEN,
        4 => OrderConst::FLAG_ORANGE,
        5 => OrderConst::FLAG_RED,
    ];

    /**
     * @return DyClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        $client = DyClient::newInstance($this->getAccessToken());
        return $client;
    }


    /**
     * @param FaPackDeliverRequest $request
     * @return PackDeliverResponse
     */
    public function delivery( FaPackDeliverRequest $request):PackDeliverResponse
    {
        $packDeliverResponse =new PackDeliverResponse();
        $tid = $request->tid;
        $packDeliverResponse->tid= $tid;
        $waybillCode = $request->waybillCode;
        $packDeliverResponse->waybillCode= $waybillCode;
        try {
            $client = $this->getClient();
            $wpCode = $request->wpCode;
            if (empty($request->items)) {
                $params = [
                    'order_id' => $tid,
//                'logistics_id' => $this->expressCodeList[$expressCode],
                    'company_code' => $wpCode,
                    'logistics_code' => $waybillCode
                ];
                $result = $client->execute('order/logisticsAdd', $params);
            } else {
                $shippedOrderInfo = [];
                foreach ($request->items as $packItem ) {
                    $shippedOrderInfo[] = [
                        'shipped_order_id' => $packItem->oid,
                        'shipped_num' => $packItem->shippedNum,
                    ];
                }
                $pack_list=[
                    "shipped_order_info"=> $shippedOrderInfo,
                    "logistics_code"=>$waybillCode,
                    'company_code' => $wpCode,
                ];
                $params = [
                    'order_id' => $tid,
                    'pack_list'=>json_encode($pack_list),
//                'logistics_id' => (string)$this->expressCodeList[$expressCode],
                    'request_id' => $request->requestId
                ];

                $result = $client->execute('order/logisticsAddMultiPack', $params);
                if (empty($result['data']['pack_list'])) {
                    Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
                    $packDeliverResponse->success=false;
                    $packDeliverResponse->errMsg="失败";
                    return $packDeliverResponse;
                }
            }
            Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
            $packDeliverResponse->success=true;
        }catch (\Throwable $throwable){
            $packDeliverResponse->success=false;
            $packDeliverResponse->errMsg=$throwable->getMessage();
            Log::errorException("发货异常",$throwable,[$request]);
        }
        return $packDeliverResponse;
    }

    /**
     * @throws ApiException
     */
    public function getOrderList(OrderListRequest $request): OrderListResponse
    {
        $response = new OrderListResponse();
        $response->requestPage = $request->getPage();

        $client = $this->getClient();
        $params = [
            'create_time_start' => $request->getStartTime(),
            'create_time_end' => $request->getEndTime(),
            'size' => $this->pageSize,
            'order_by' => 'create_time',
            'order_asc' => false, //  默认 false 降序（大到小），排序类型，小到大或大到小，
            'page' => $request->getPage() - 1,
            'after_sale_status_desc' => 'all'
        ];

        if ($request->isPullOnlyWaitDelivery()) {
            $params['combine_status']['order_status'] = 2;
        }

        $result = $client->execute('order/searchList', $params);
        $client->handleErrorCode($result);

        $shop_order_list = (array)$result['data']['shop_order_list'];
        if (count($shop_order_list) == $this->pageSize) {
            $response->hasNext = true;
        }
        $response->total = $result['data']['total'] ?? -1;
        $response->setOrderList($this->formatToOrders($shop_order_list));

        return $response;
    }

    /**
     * @inheritDoc
     */
    public function getOrderListByIncr(OrderListRequest $request): OrderListResponse
    {
        $response = new OrderListResponse();
        $response->requestPage = $request->getPage();

        $client = $this->getClient();
        $params = [
            'update_time_start' => $request->getStartTime(),
            'update_time_end' => $request->getEndTime(),
            'size' => $this->pageSize,
            'order_by' => 'update_time',
            'order_asc' => false, //  默认 false 降序（大到小），排序类型，小到大或大到小，
            'page' => $request->getPage() - 1,
            'after_sale_status_desc' => 'all'
        ];

        if ($request->isPullOnlyWaitDelivery()) {
            $params['combine_status']['order_status'] = 2;
        }

        $result = $client->execute('order/searchList', $params);
        $client->handleErrorCode($result);

        $shop_order_list = (array)$result['data']['shop_order_list'];
        if (count($shop_order_list) == $this->pageSize) {
            $response->hasNext = true;
        }
        $response->total = $result['data']['total'] ?? -1;
        $response->setOrderList($this->formatToOrders($shop_order_list));

        return $response;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $orderOriginal): array
    {
        $orderItems = [];
        $promiseShipAt = [];
        $tid = $orderOriginal['order_id'];
        $orderStatus = $this->formatOrderStatus($orderOriginal['order_status']);

        foreach ($orderOriginal['sku_order_list'] as $item) {
            $total = $item['pay_amount'];
//            $sku_value = '';
//            foreach ($item['spec'] as $desc) {
//                $sku_value .= $desc['value'] . ';';     //不带规格名：XL;红色;
//            }
            $skuList = [];
            if (!empty($item['spec'])){
                foreach ($item['spec'] as $desc) {
                    $skuList[] = ['name'=>$desc['name'], 'value'=>$desc['value']];
                }
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);
            $status = $this->formatOrderStatus($item['order_status']);
            $refundStatus = $this->hasRefundStatus($item['after_sale_info']['after_sale_status']);
            $orderItem = [
                "tid" => $tid, //主订单
                "oid" => (string)$item['order_id'], //子订单号
                "type" => $this->getPlatformType(), //订单类型
                "order_status" => $orderStatus, //订单状态
                "payment" => MathUtil::formatToYuan($total), //实付金额
                "total_fee" => MathUtil::formatToYuan($total), //总金额
                "discount_fee" => MathUtil::formatToYuan($item['promotion_amount']), //优惠金额
//                "goods_pic" => $item['product_pic'] ?? "", //商品图片
                "goods_title" => $item['product_name'], //商品标题
                "sku_price" => MathUtil::formatToYuan($item['origin_amount']), //商品单价
                "sku_num" => $item['item_num'], //商品数量
                "num_iid" => $item['product_id'], //商品id
                "sku_pic" => $item['product_pic'], //sku图片
                "sku_id" => $item['sku_id'] ?? '', //sku id
                "sku_value" => $skuValue, // sku值
                "sku_value1" => $skuValue1, // sku值1
                "sku_value2" => $skuValue2, // sku值2
                "outer_iid" => $item['code'], //商家外部商品编码,商家编码code
                "outer_sku_iid" => $item['out_sku_id'] ?? '', //商家外部sku编码
                "order_created_at" => date('Y-m-d H:i:s', $orderOriginal['create_time']), //订单创建时间
                "order_updated_at" => date('Y-m-d H:i:s', $orderOriginal['update_time']), //订单修改时间
                "status" => $status,
                "after_sales_status" => $refundStatus, //
                'send_at' => (isset($item['ship_time']) && $item['ship_time']) ? date('Y-m-d H:i:s', $item['ship_time']) : null,//发货时间
                'is_comment' => (isset($item['is_comment']) && $item['is_comment'] == OrderConst::IS_COMMENT_YES) ? OrderConst::IS_COMMENT_YES : 0, // 是否评价
            ];
            //子订单发货时间
            if ($item['exp_ship_time']) {
                $promiseShipAt[] = $item['exp_ship_time'];
            }
            $orderItems[] = $orderItem;
        }
        !empty($promiseShipAt) && sort($promiseShipAt);

        //主订单退款状态
        $refund_status = in_array(OrderConst::REFUND_STATUS_YES, array_column($orderItems, 'after_sales_status')) ? OrderConst::REFUND_STATUS_YES : OrderConst::REFUND_STATUS_NO;

        //判读部分退款还是全部退款 有一个未退款就是部分退款(主订单状态是退款或者子订单中有退款 并且有未退款的子订单)
        if (($refund_status == OrderConst::REFUND_STATUS_YES || in_array(OrderConst::REFUND_STATUS_YES, array_column($orderItems, 'after_sales_status'))) && in_array(OrderConst::REFUND_STATUS_NO, array_column($orderItems, 'refund_status'))) {
            $refund_status = OrderConst::REFUND_STATUS_PART;
        }
        //部分发货且部分退款 修正订单状态
        if ($orderStatus == OrderConst::ORDER_STATUS_PART_DELIVERED) {
            $noOrderItem = collect($orderItems)->whereIn('status', [OrderConst::ORDER_STATUS_PAYMENT, OrderConst::ORDER_STATUS_FAILED, OrderConst::ORDER_STATUS_CLOSED]);
            $hasNoRefund = collect($noOrderItem)->where('after_sales_status', 0);
            //剩下未发货子订单 全部退款了 修正订单状态为已发货
            if (count($hasNoRefund) == 0) {
                $orderStatus = OrderConst::ORDER_STATUS_DELIVERED;
            }
            //部分发货主订单上没有发货时间 取子订单最后发货时间
            $shipTime = $this->getShipTime($orderOriginal['sku_order_list']);

        }
        $city = $orderOriginal['post_addr']['city']['name'] ?? '';
        $receiverState = $orderOriginal['post_addr']['province']['name'] ?? '';
        $receiverDistrict = $orderOriginal['post_addr']['town']['name'] ?? '';
        $cipher_info = [
            'receiver_phone_ciphertext' => $orderOriginal['encrypt_post_tel'],
            'receiver_name_ciphertext' => $orderOriginal['encrypt_post_receiver'],
            'receiver_address_ciphertext' => $orderOriginal['post_addr']['encrypt_detail'],
            'receiver_phone_mask' => $orderOriginal['mask_post_tel'],
            'receiver_name_mask' => $orderOriginal['mask_post_receiver'],
            'receiver_address_mask' => $orderOriginal['mask_post_addr']['detail'],
            'oaid' => $orderOriginal['open_address_id'] ?? '',
        ];

//        $receiver_phone = $this->extractSearchIndex($cipher_info['receiver_phone_ciphertext'], '$');
//        $receiver_name = $this->extractSearchIndex($cipher_info['receiver_name_ciphertext'], '#');
//        $receiver_address = $this->extractSearchIndex($cipher_info['receiver_address_ciphertext'], '#');
//        $receiver_phone = $cipher_info['receiver_phone_mask'];
        $receiver_phone = $cipher_info['receiver_phone_mask'];
        $receiver_name = $cipher_info['receiver_name_mask'];
        $receiver_address = $cipher_info['receiver_address_mask'];


        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->getPlatformType(), //订单类型
            "order_type" => OrderConst::ORDER_TYPE_PT_ORDER, //订单业务类型
//            "express_code" => array_search($trade['logistics_id'], $this->expressCodeList) ?: null , //快递公司代码
//            "express_no" => array_get($trade, 'logistics_code', null), //快递单号
            "buyer_id" => $orderOriginal['doudian_open_id'], //买家ID
            "buyer_nick" => '', //买家昵称
            "seller_nick" => $orderOriginal['shop_name'], //卖家昵称
            "shop_title" => $orderOriginal['shop_name'], //卖家昵称
            "order_status" => $orderStatus, //订单状态
            "after_sales_status" => $refund_status, //退款状态
            "receiver_province" => $receiverState, //收货人省份
            "receiver_city" => $city, //收货人城市
            "receiver_district" => $receiverDistrict, //收货人地区
            "receiver_town" => $orderOriginal['post_addr']['street']['name'] ?? '', //收货人街道
            "receiver_name" => $receiver_name, //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => 0, //收件人邮编
            "receiver_address" => $receiver_address, //收件人详细地址
            "payment" => MathUtil::formatToYuan($orderOriginal['pay_amount']), //实付金额
            "total_fee" => MathUtil::formatToYuan($orderOriginal['order_amount']), //总金额
            "discount_fee" => MathUtil::formatToYuan($orderOriginal['promotion_amount']), //优惠金额
            "post_fee" => MathUtil::formatToYuan($orderOriginal['post_amount']), //运费
            //"seller_flag" => OrderConst::FLAG_NONE, //卖家备注旗帜
            "seller_flag" => $this->formatOrderFlag($orderOriginal['seller_remark_stars']), //卖家备注旗帜
            "seller_memo" => empty($orderOriginal['seller_words']) ? '[]' : json_encode([$orderOriginal['seller_words']], 320), //卖家备注
            "buyer_message" => $orderOriginal['buyer_words'], //买家留言
            "has_buyer_message" => empty($orderOriginal['buyer_words']) ? 0 : 1, //买家留言
            "order_created_at" => date('Y-m-d H:i:s', $orderOriginal['create_time']), //订单创建时间
            "order_updated_at" => date('Y-m-d H:i:s', $orderOriginal['update_time']), //订单修改时间
            "send_at" => !empty($orderOriginal['ship_time']) ? date('Y-m-d H:i:s', $orderOriginal['ship_time']) :
                (!empty($shipTime) ? date('Y-m-d H:i:s', $shipTime) : null), //发货时间
            "pay_at" => !empty($orderOriginal['pay_time']) ? date('Y-m-d H:i:s', $orderOriginal['pay_time']) : null, //支付时间
            'is_comment' => (isset($orderOriginal['is_comment']) && $orderOriginal['is_comment'] == OrderConst::IS_COMMENT_YES) ? OrderConst::IS_COMMENT_YES : 0, // 是否评价
            'goods_total_num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => 0, // 是否预售1是0否
            'promise_ship_at' => !empty($promiseShipAt) ? date('Y-m-d H:i:s', $promiseShipAt[0]) :
                (!empty($orderOriginal['exp_ship_time']) ? date('Y-m-d H:i:s', $orderOriginal['exp_ship_time']) : null),// 承诺发货时间
//            'district_code' => $trade['post_addr']['town']['id']??0,
            'items' => $orderItems,
            'cipher_info' => $cipher_info,
//            'order_extra' => [
//                'order_biz_type' => $trade['biz'] == 8 ? OrderExtra::BIZ_TYPE_QUALITY_INSPECTION : OrderExtra::BIZ_TYPE_GENERAL, // 订单业务类型
//            ],
        ];

//        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);

        //可能为空
        if (empty($orderData['express_code']) || empty($orderData['express_no'])) {
            unset($orderData['express_code']);
            unset($orderData['express_no']);
        }

//        var_dump($orderData);
        return $orderData;
    }

    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * 转换订单状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function formatOrderStatus($status): int
    {
        if (!isset($this->orderStatusMap[$status])) {
            return OrderConst::ORDER_STATUS_CLOSED;
        }
        return $this->orderStatusMap[$status];
    }

    /**
     * 转换退款状态
     * 只需要定义没有退款状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function hasRefundStatus($status): int
    {
        if (empty($this->refundStatusMap)) {
            throw new InvalidArgumentException('未定义退款状态！');
        }
        if (isset($this->refundStatusMap[$status])) {
            return $this->refundStatusMap[$status];
        }
        return OrderConst::REFUND_STATUS_NO;
    }

    /**
     * 转换订单旗帜
     * @param $flag
     * @return string
     * <AUTHOR>
     */
    public function formatOrderFlag($flag): string
    {
        if (!isset($this->orderFlagMap[$flag])) {
            throw new InvalidArgumentException('未定义的订单旗帜：' . $flag);
        }
        return $this->orderFlagMap[$flag];
    }

    private function getShipTime($skuOrderList)
    {
        $skuShipTime = array_column($skuOrderList, 'ship_time');
        rsort($skuShipTime);

        return $skuShipTime[0];
    }

    public function getPlatformType(): int
    {
        return PlatformConst::PLATFORM_TYPE_DY;
    }

    /**
     * 提取搜索串
     * @see https://op.jinritemai.com/docs/guide-docs/118/594
     * @param string $string
     * @param string $sep
     * @return string
     */
    private function extractSearchIndex(string $string, string $sep): string
    {
        if (empty($string)){
            return '';
        }
        $str = $string;
        if (str_starts_with($string, $sep)) {
            $endPos = strpos($string, $sep, 1);
            $str = substr($string, 1, $endPos - 1);
        }
        return $str;
    }

    /**
     * @inheritDoc
     */
    public function getAddressList(): array
    {
        $client = $this->getClient();
        $params = [];
        $result = $client->execute('address/getProvince', $params);
        $addressList = [];
        foreach ($result['data'] as $province) {
            $params = [
                'province_id' => $province['province_id'],
            ];
            $result2 = $client->execute('address/getAreasByProvince', $params);

            $provinceList = $this->formatAddress($result2['data']);
            $addressList[] = $provinceList[0];
        }
        return $addressList;
    }

    protected function formatAddress(array $list, $level = 1): array
    {
        $resArr = [];
        foreach ($list as $item) {
            $parent_code = $item['father_code'];
            if ($level == 1) {
                $parent_code = 1;
            }
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            if (!empty($item['sub_districts']) && is_array($item['sub_districts'])) {
                $arr['sub'] = $this->formatAddress($item['sub_districts'], $level + 1);
            }
            $resArr[] = $arr;
        }
        return $resArr;
    }

    public function batchGetOrderInfo(array $tidArr): array
    {
        $client = $this->getClient();
        $paramsArr = [];
        foreach ($tidArr as $tid) {
            $params = [
                'shop_order_id' => $tid,
            ];
            $paramsArr[] = [
                "url" => $client->getApiFullUrl("order/orderDetail"),
                "params" => $client->buildRequestData($params, "order.orderDetail")
            ];
        }
        $resultArr = array();
        $tranFunction = function ($index, CurlResult $curlData) use (&$resultArr, $client) {
            $response = $client->handleResponse($curlData->body);
            $resultArr[$index] = Arr::get($response, 'data.shop_order_detail',[]);
        };
        $client->poolCurl($paramsArr, "get", $tranFunction);
        $resultArr = $this->formatToOrders($resultArr);
        return $resultArr;
    }

    /**
     * @inheritDoc
     */
    public function batchEditOrderRemark(array $tidArr, $sellerFlag = null, $sellerMemo = null): array
    {
        $client = $this->getClient();
        $paramsArr = [];
        foreach ($tidArr as $tid) {
            $params = [
                'order_id' => $tid,
                'remark' => $sellerMemo,
            ];
            // 旗标
            if ($sellerFlag) {
                $star = [
                    'star' => array_flip($this->orderFlagMap)[$sellerFlag],
                    'is_add_star' => 'true'
                ];
                $params = array_merge($params, $star);
            }
            $apiMethod = "order/addOrderRemark";
            $paramsArr[] = [
                "url" => $client->getApiFullUrl($apiMethod),
                "params" => $client->buildRequestData($params, $apiMethod)
            ];
        }
        $responseArr = [];
        $tranFunction = function ($index, CurlResult $curlData) use (&$responseArr,$tidArr, $client) {
            $commonResponse = new CommonResponse();
            $commonResponse->setId($tidArr[$index]);
            try {
                $response = $client->handleResponse($curlData->body);
                $code = Arr::get($response, 'code', 0);
                if ($code == 10000) {
                    $commonResponse->setIsSuccess(true);
                }
            } catch (\Throwable $e) {
                $commonResponse->setFailByThrowable($e);
            }
            $responseArr[$index] = $commonResponse;
        };
        $client->poolCurl($paramsArr, "get", $tranFunction);
        return $responseArr;
    }

    /**
     * @inheritDoc
     */
    public function batchDecrypt(array $requests): array
    {
        $client = $this->getClient();
        $cipher_infos = [];
        foreach ($requests as $decryptRequest) {
            $cipher_infos[] = [
                'auth_id' => $decryptRequest->tid,
                'cipher_text' => $decryptRequest->ciphertext,
            ];
        }
        $result = $client->execute('order/batchDecrypt', ['cipher_infos' => $cipher_infos]);
        $client->handleErrorCode($result);
        $resultData = Arr::get($result, 'data.decrypt_infos',[]);
        $responseArr = [];
        foreach ($resultData as $index => $resultDatum) {
            $decryptResponse = new DecryptResponse();
            $decryptResponse->tid = $resultDatum['auth_id'];
            $decryptResponse->filed = $requests[$index]->filed;
            $decryptResponse->decryptText = $resultDatum['decrypt_text'];
            $responseArr[] = $decryptResponse;
        }
        return $responseArr;
    }

    public function getOrderInfo(string $tid): array
    {
        $client = $this->getClient();
        $params = [];
        $result = $client->execute('order/orderDetail', $params);
        $client->handleErrorCode($result);
        $data = Arr::get($result, 'data.shop_order_detail',[]);
        return $this->formatToOrder($data);
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $wpShop = null): array
    {
        switch ($apiMethod) {
            case 'logistics.newCreateOrder';
                $receiver_info = [
                    'address' => [
                        'city_name' => $order['receiver_city'],
                        'country_code' => 'CHN',
                        'detail_address' => $order['orderCipherInfo']['receiver_address_ciphertext'],
                        'district_name' => $order['receiver_district'],
                        'province_name' => $order['receiver_province'],
                        'street_name' => $order['receiver_town']
                    ],
                    'contact' => [
                        'mobile' => $order['orderCipherInfo']['receiver_phone_ciphertext'],
                        'name' => $order['orderCipherInfo']['receiver_name_ciphertext'],
                        'phone' => $order['receiver_tel'] ?? '',
                    ]
                ];
                $apiParams['order_infos'][0]['receiver_info'] = $receiver_info;
                break;
            default:
                break;
        }
        return $apiParams;
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams): array
    {
        $client = $this->getClient();
        if ($apiMethod == 'logistics.getShopKey') {
            $requestData = $client->buildRequestData($apiParams, $apiMethod);
            $paramsStr = urldecode(http_build_query($requestData));
            return ['paramsBase64Str' => base64_encode($paramsStr)];
        }
        $result = $client->execute($apiMethod, $apiParams, $requestMethod);
        return $result;
    }

    /**
     * 添加数据推送店铺
     * @param Shop $shop
     * @return void
     * @throws ApiException
     * @throws GuzzleException
     */
    public function dataPushAddShop(Shop $shop): void
    {
        $client = $this->getClient();
        $pushDbId = $client::getConfig('push_db_id', '');
        $params = [
            'shop_id' => $shop->open_shop_id,
            'rds_instance_id' => $pushDbId,
            'history_days' => 7, // 历史推送天数。(0表示不推送历史数据，空表示跟随应用)
        ];

        $result = $client->execute('openCloud/ddpAddShop', $params);
        $client->handleErrorCode($result);

    }

    /**
     * 删除数据推送店铺
     * @param Shop $shop
     * @return void
     * @throws ApiException
     * @throws GuzzleException
     */
    public function dataPushDeleteShop(Shop $shop): void
    {
        $client = $this->getClient();
        $params = [
            'shop_id' => $shop->open_shop_id,
        ];

        $result = $client->execute('openCloud/ddpDeleteShop', $params);
        $client->handleErrorCode($result);
    }
    public function sendCheckMergeOrder(array $orderList): array
    {

        $merge_list = [];
        foreach ($orderList as $order) {
            $tid = $order['tid'];
            // 超过15天的订单不允许合单
            if (time() - strtotime($order['pay_at']) > 15 * 86400) {
                continue;
            }
            $merge_list[] = [
                'order_id' => $tid,
                'open_address_id' => $order['order_cipher_info']['oaid'],
            ];
        }
        $mergeListChunk = array_chunk($merge_list, 100); // 取100个

        if (empty($mergeListChunk[0])){
            return [];
        }
        $client = $this->getClient();
        $params = [
            'merge_list' => $mergeListChunk[0],
        ];
        // 合单只能是2-100
        if (count($params['merge_list']) < 2 || count($params['merge_list']) > 100) {
            Log::info('sendCheckMergeOrderError',$params);
            return [];
        }
        $result = $client->execute('order/merge', $params, 'post');
        try {
            $client->handleErrorCode($result);
        } catch (\Exception $e) {
            Log::info('sendCheckMergeOrder:error', [$e->getMessage()]);
            return [];
        }
        $orderList = $result['data']['order_list'];
//        $orderList = array_map(function ($item) {
//            $arr = explode(',', $item);
//            $newArr = [];
//            foreach ($arr as $index => $item) {
//                $newArr[] = $item.'A';
//            }
//            return implode(',', $newArr);
//        }, $order_list);
        return $orderList;
    }
}