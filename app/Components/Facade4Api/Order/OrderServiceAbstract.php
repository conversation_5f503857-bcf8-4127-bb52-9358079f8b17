<?php

namespace App\Components\Facade4Api\Order;

use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Components\Facade4Api\Order\Request\DecryptRequest;
use App\Components\Facade4Api\Order\Request\FaPackDeliverRequest;
use App\Components\Facade4Api\Order\Request\FaSearchIndexParam;
use App\Components\Facade4Api\Order\Request\OverseasOrderListRequest;
use App\Components\Facade4Api\Order\Response\CommonResponse;
use App\Components\Facade4Api\Order\Response\FaSearchIndexResponse;
use App\Components\Facade4Api\Order\Response\OrderListResponse;
use App\Components\Facade4Api\Order\Response\PackDeliverResponse;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Model\Shop\Shop;
use App\Utils\Common;
use App\Utils\CommonUtil;
use Hyperf\Collection\Arr;

abstract class OrderServiceAbstract implements OrderServiceInterface
{
    protected int $pageSize = 100;
//    protected bool $hasNext = false;
    protected string $accessToken = '';

    // 订单同步间隔（分钟）
    protected int $orderSyncTimeInterval = 60 * 2;
    // 订单增量同步间隔（分钟）
    protected int $orderSyncIncrTimeInterval = 60 * 2;
    // 订单同步的结束时间偏移量（分钟） 结束时间=当前时间减多少分钟
    protected int $orderSyncTimeOffset = 1;

    /**
     * 计算订单需要同步的次数
     * @param string $beginAt
     * @param string $endAt
     * @return int
     */
    public function calcOrderSyncNumber(string $beginAt, string $endAt): int
    {
        return (int)ceil((strtotime($endAt) - strtotime($beginAt)) / 60 / $this->orderSyncIncrTimeInterval);
    }

    /**
     * 计算订单增量时间范围
     * @param string $beginAt
     * @param string $endAt
     * @param int $i
     * @param int $len
     * @return array
     */
    public function calcOrderSyncTimeRangeByIncr(string $beginAt, string $endAt, int $i, int $len): array
    {
        $orderTimeInterval = $this->orderSyncIncrTimeInterval;
        list($startTime, $endTime) = $this->calcTimeRangeByTimeInterval($i, $len, $orderTimeInterval, $beginAt, $endAt);
        return [$startTime, $endTime];
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * @param string $accessToken
     */
    public function setAccessToken(string $accessToken): void
    {
        $this->accessToken = $accessToken;
    }


    /**
     * 包含密文的地址 md5
     * @param $order
     * @param $cipherInfo
     * @return string
     */
    public function genAddressMd5($order, $cipherInfo): string
    {
        $md5_receiver_address = CommonUtil::filterZeroChar($order['receiver_address']);
        $addressArr = [
            $order['shop_id'] ?? 0,
            $order['buyer_id'],
            $order['buyer_nick'],
            $order['shop_title'],
            $order['seller_nick'],
            $order['receiver_province'],
            $order['receiver_city'],
            $order['receiver_district'],
            $md5_receiver_address,
            $order['receiver_name'],
            $order['receiver_phone'],
//            $cipherInfo['receiver_phone_ciphertext'] ,
//            $cipherInfo['receiver_name_ciphertext'],
//            $cipherInfo['receiver_address_ciphertext'] ?? '',
            $cipherInfo['oaid'] ?? '',
        ];
        return md5(implode(',', $addressArr));
    }

    /**
     * 疑似地址 md5
     * receiver_md5 是地址的 md5，但是由于现在都已经加密了，所以是疑似地址
     * @param mixed $order
     * @param mixed $cipherInfo
     * @return string
     */
    public function genReceiverMd5(mixed $order, mixed $cipherInfo): string
    {
        $md5_receiver_address = CommonUtil::filterZeroChar($order['receiver_address']);
        $addressArr = [
            $order['shop_id'],
            $order['buyer_id'],
            $order['buyer_nick'],
            $order['receiver_province'],
            $order['receiver_city'],
            $order['receiver_district'],
            $md5_receiver_address,
            $order['receiver_name'],
            $order['receiver_phone'],
        ];
        return md5(implode(',', $addressArr));
    }

    /**
     * @param int $i
     * @param int $len
     * @param float|int $orderTimeInterval
     * @param string $beginAt
     * @param string $endAt
     * @return array
     */
    public function calcTimeRangeByTimeInterval(int $i, int $len, float|int $orderTimeInterval, string $beginAt, string $endAt): array
    {
        $addMinutes = $i * $orderTimeInterval;
        $startTime = strtotime("+$addMinutes minute", strtotime($beginAt));
        $endTime = strtotime("+$orderTimeInterval minute", $startTime);
        // 当前结束时间不能超出结束时间
        if ($endTime > strtotime($endAt)) {
            $endTime = strtotime($endAt);
        }
        if ($i == ($len - 1) && $this->orderSyncTimeOffset > 0) {
            // 减去偏移量，避免漏单
            $endTime = $endTime - $this->orderSyncTimeOffset * 60;
            // 结束时间不能小于开始时间
            if ($endTime < $startTime) {
                $startTime = $endTime - 60;
            }
        }
        return array($startTime, $endTime);
    }

    /**
     * 获取加密内容索引字段
     * @param FaSearchIndexParam[] $params
     * @return FaSearchIndexResponse[]
     * @throws ApiException
     */

    public function getSearchIndex(array $params): array
    {
        ApiException::throwException(ErrorConstants::SYSTEM_ERROR[0], "未实现getSearchIndex方法");
    }

    /**
     * 批量请求订单详情
     * @param array $tidArr
     * @return array
     */
    abstract public function batchGetOrderInfo(array $tidArr): array;

    /**
     * 获取订单详情
     * @param string $tid
     * @return array
     */
    abstract public function getOrderInfo(string $tid): array;

    /**
     * 批量订单备注
     * @param array $tidArr
     * @param $sellerFlag
     * @param $sellerMemo
     * @return CommonResponse[]
     */
    abstract public function batchEditOrderRemark(array $tidArr, $sellerFlag = null, $sellerMemo = null): array;

    /**
     * 批量解密
     * @param DecryptRequest[] $requests
     * @return DecryptRequest[]
     */
    abstract public function batchDecrypt(array $requests): array;

    /**
     * 通过订单填充 api参数
     * @param $apiMethod
     * @param $apiParams
     * @param  $order
     * @param null $wpShop
     * @return array
     */
    abstract public function fillApiParamByOrder($apiMethod, $apiParams, $order, $wpShop = null):array;

    /**
     * 通过自定义参数请求
     * @param $requestMethod
     * @param $apiMethod
     * @param $apiParams
     * @return array
     */
    abstract public function sendByCustom($requestMethod, $apiMethod, $apiParams): array;


    /**
     * 跨境订单
     * @param OverseasOrderListRequest $request
     * @return OrderListResponse
     */
    public function getOverseasListByIncr(OverseasOrderListRequest $request):OrderListResponse
    {
        return new OrderListResponse();
    }

    /**
     * @throws ApiException
     */
    public function uploadExtraLogistics(FaPackDeliverRequest $request): PackDeliverResponse
    {
        throw new ApiException(ErrorConstants::SYSTEM_ERROR[0], "未实现appendDelivery方法");
    }

    /**
     * 添加数据推送店铺
     * @param Shop $shop
     * @return void
     */
    public function dataPushAddShop(Shop $shop): void
    {
    }
    /**
     * 删除数据推送店铺
     * @param Shop $shop
     * @return void
     */
    public function dataPushDeleteShop(Shop $shop): void
    {
    }

    /**
     * 判断订单号是否能合单
     * @param Order[] $orderList
     * @return array
     * @throws ErrorCodeException
     */
    public function sendCheckMergeOrder(array $orderList): array
    {
        throw new ApiException(ErrorConstants::SYSTEM_ERROR[0], "未实现sendCheckMergeOrder方法");
    }

    /**
     * @param array $skuList
     * @return array|string[]
     */
    protected function getSkuValueAnd12(array $skuList): array
    {
        $skuValue = '默认;';
        $skuValue1 = '';
        $skuValue2 = '';
        if (empty($skuList)) {
            return [$skuValue, $skuValue1, $skuValue2];
        }
        $skuValue = implode(';', Arr::pluck($skuList,'value'));
        // 循环匹配，如果匹配到颜色那就优先赋值给 $skuValue1，如果匹配到尺寸那就优先赋值给 $skuValue2
        foreach ($skuList as $index => $item) {
            if ($index > 1) { // 第三层开始不处理
                break;
            }
            if (preg_match('/颜色/u', $item['name']) && empty($skuValue1)) {
                $skuValue1 = $item['value'];
            }
            if (preg_match('/(身高|尺码|尺寸)/u', $item['name']) && empty($skuValue2)) {
                $skuValue2 = $item['value'];
            }
        }
        // 循环结束，没赋值的
        if (empty($skuValue1)) {
            // 如果第一个没有赋值给 value2，那么就取第一个给 value1
            if ($skuList[0]['value'] != $skuValue2){
                $skuValue1 = $skuList[0]['value']??'';
            }else{
                $skuValue1 = $skuList[1]['value']??'';
            }
        }
        if (empty($skuValue2) && !empty($skuList[1])) {
            // 如果第二个没有赋值给 value1，那么就取第二个给 value2
            if ($skuList[1]['value'] != $skuValue1){
                $skuValue2 = $skuList[1]['value']??'';
            }else{
                $skuValue2 = $skuList[0]['value']??'';
            }
        }
        return [$skuValue, $skuValue1, $skuValue2];
    }
}